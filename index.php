<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente Web SAP Business One</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="assets/css/login.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Pantalla de Inicio de Sesión -->
    <div id="login-container" class="login-container active">
        <div class="login-form">
            <div class="login-header">
                <img src="assets/images/sap-logo.svg" alt="SAP" class="sap-logo">
                <h2>SAP Business One</h2>
                <p>Cliente Web</p>
            </div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="server">Servidor:</label>
                    <input type="text" id="server" name="server" value="https://192.168.2.249:50000/b1s/v2" required>
                </div>
                
                <div class="form-group">
                    <label for="database">Base de Datos:</label>
                    <div class="database-switch">
                        <input type="radio" id="db-dev" name="database" value="CAPA" checked>
                        <label for="db-dev" class="db-option dev">
                            <i class="fas fa-database"></i>
                            Desarrollo (CAPA)
                        </label>
                        
                        <input type="radio" id="db-prod" name="database" value="SBO_ECOM">
                        <label for="db-prod" class="db-option prod">
                            <i class="fas fa-server"></i>
                            Producción (SBO_ECOM)
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">Usuario:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Contraseña:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Conectar
                </button>
                
                <div class="login-status" id="login-status"></div>
            </form>
        </div>
    </div>

    <!-- Aplicación Principal -->
    <div id="main-app" class="main-app">
        <!-- Encabezado -->
        <header class="app-header">
            <div class="header-left">
                <img src="assets/images/sap-logo.svg" alt="SAP" class="header-logo">
                <span class="company-name" id="company-name">SAP Business One</span>
            </div>
            
            <div class="header-center">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Buscar...">
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-info">
                    <span id="current-user">Usuario</span>
                    <span id="current-database" class="database-badge">CAPA</span>
                </div>
                <button class="logout-btn" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </header>

        <!-- Menú de Navegación -->
        <nav class="main-nav">
            <ul class="nav-menu">
                <li class="nav-item" data-module="sales">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Ventas - CxC</span>
                </li>
                <li class="nav-item" data-module="purchasing">
                    <i class="fas fa-shopping-bag"></i>
                    <span>Compras - CxP</span>
                </li>
                <li class="nav-item" data-module="inventory">
                    <i class="fas fa-boxes"></i>
                    <span>Inventario</span>
                </li>
                <li class="nav-item" data-module="partners">
                    <i class="fas fa-users"></i>
                    <span>Socios de Negocio</span>
                </li>
                <li class="nav-item" data-module="banking">
                    <i class="fas fa-university"></i>
                    <span>Bancos</span>
                </li>
                <li class="nav-item" data-module="financials">
                    <i class="fas fa-chart-line"></i>
                    <span>Finanzas</span>
                </li>
                <li class="nav-item" data-module="reports">
                    <i class="fas fa-file-alt"></i>
                    <span>Informes</span>
                </li>
                <li class="nav-item" data-module="administration">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </li>
            </ul>
        </nav>

        <!-- Área de Contenido -->
        <main class="content-area" id="content-area">
            <div class="dashboard" id="dashboard">
                <h1>Bienvenido a SAP Business One</h1>
                <div class="dashboard-widgets">
                    <div class="widget">
                        <h3>Acciones Rápidas</h3>
                        <div class="quick-actions">
                            <button class="quick-btn" data-action="sales-order">
                                <i class="fas fa-plus"></i>
                                Nueva Orden de Venta
                            </button>
                            <button class="quick-btn" data-action="purchase-order">
                                <i class="fas fa-plus"></i>
                                Nueva Orden de Compra
                            </button>
                            <button class="quick-btn" data-action="item-master">
                                <i class="fas fa-plus"></i>
                                Nuevo Artículo
                            </button>
                            <button class="quick-btn" data-action="business-partner">
                                <i class="fas fa-plus"></i>
                                Nuevo Socio de Negocio
                            </button>
                        </div>
                    </div>
                    
                    <div class="widget">
                        <h3>Documentos Recientes</h3>
                        <div id="recent-documents">
                            Cargando...
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Superposición de Carga -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="spinner"></div>
        <p>Cargando...</p>
    </div>

    <!-- Scripts -->
    <script src="assets/js/core/app.js"></script>
    <script src="assets/js/core/auth.js"></script>
    <script src="assets/js/core/api.js"></script>
    <script src="assets/js/core/navigation.js"></script>
    <script src="assets/js/modules/sales.js"></script>
    <script src="assets/js/modules/purchasing.js"></script>
    <script src="assets/js/modules/inventory.js"></script>
    <script src="assets/js/modules/partners.js"></script>
    <script src="assets/js/modules/banking.js"></script>
    <script src="assets/js/modules/financials.js"></script>
    <script src="assets/js/modules/reports.js"></script>
    <script src="assets/js/modules/administration.js"></script>
    
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            App.init();
        });
    </script>
</body>
</html>
