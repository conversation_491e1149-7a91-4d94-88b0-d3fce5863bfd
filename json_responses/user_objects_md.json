{"metadata": {"timestamp": "2025-06-30 22:38:04", "description": "User Defined Objects Metadata", "endpoint": "UserObjectsMD", "http_code": 200, "success": true}, "response": {"odata.metadata": "https://*************:50000/b1s/v1/$metadata#UserObjectsMD", "value": [{"TableName": "EPY_PLPY", "Code": "EPY_PLPY", "LogTableName": "AEPY_PLPY", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Parametrizaciones", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_PLPY", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_DEMP", "LogTableName": "AEPY_DEMP", "Code": "EPY_PLPY", "ObjectName": "EPY_DEMP"}, {"SonNumber": 2, "TableName": "EPY_DSUC", "LogTableName": "AEPY_DSUC", "Code": "EPY_PLPY", "ObjectName": "EPY_DSUC"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_PLPY"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_PLPY"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_TR", "Code": "EPY_TR", "LogTableName": "AEPY_TR", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Maestro Tarjeta Retenciones", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_TR", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_TR"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_TR"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_MRET", "Code": "EPY_MRET", "LogTableName": "AEPY_MRET", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Maestro de Retenciones", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_MRET", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_MRET"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_MRET"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_RSIT", "Code": "EPY_RSIT", "LogTableName": "AEPY_RSIT", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Maestro de situación", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_RSIT", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_RSIT"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_RSIT"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_RIDT", "Code": "EPY_RIDT", "LogTableName": "AEPY_RIDT", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "M. identificación Proveedor", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_RIDT", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_RIDT"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_RIDT"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_RTIC", "Code": "EPY_RTIC", "LogTableName": "AEPY_RTIC", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "<PERSON><PERSON> Comproban<PERSON>", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_RTIC", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_RTIC"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_RTIC"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_RCRT", "Code": "EPY_RCRT", "LogTableName": "AEPY_RCRT", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "M. Cptos. R. por Situación", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_RCRT", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_RCRT"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_RCRT"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_RCRI", "Code": "EPY_RCRI", "LogTableName": "AEPY_RCRI", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "M. Conceptos IVA (Retención)", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_RCRI", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_RCRI"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_RCRI"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_DRDI", "Code": "EPY_DRDI", "LogTableName": "AEPY_DRDI", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Retención de Impuestos", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_DRDI", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_DRDI"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_DRDI"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_CRAP", "Code": "EPY_CRAP", "LogTableName": "AEPY_CRAP", "CanCreateDefaultForm": "tNO", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tNO", "CanYearTransfer": "tNO", "Name": "Lista Cert. no retenciones", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_CRAP", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_DCR", "LogTableName": "AEPY_DCR", "Code": "EPY_CRAP", "ObjectName": "EPY_DCR"}], "UserObjectMD_FindColumns": [], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_ARIR", "Code": "EPY_ARIR", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Conceptos Retenciones IVA/RENTA", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_ARIR", "EnableEnhancedForm": "tNO", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"UTF-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"UDO_FT_EPY_ARIR\" FormType=\"UDO_FT_EPY_ARIR\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_ARIR\" title=\"Conceptos Retenciones IVA/RENTA\" visible=\"1\" default_button=\"\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"255\" client_width=\"\" client_height=\"\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_ARIR\" mode=\"3\">\r        <datasources>\r          <DataTables />\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_ARIR\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource type=\"9\" size=\"10\" uid=\"FolderDS\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item top=\"6\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Code\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"0_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"0_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Code\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"0\" text_style=\"0\" top=\"6\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Code\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"0_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"Code\" />\r              </specific>\r            </item>\r            <item top=\"6\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Artículo\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"13_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"13_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Código Artículo\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"1\" text_style=\"0\" top=\"6\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Artículo\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"13_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_CART\" />\r              </specific>\r            </item>\r            <item top=\"21\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Artículo\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"14_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"14_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Nombre Artículo\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"2\" text_style=\"0\" top=\"21\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Artículo\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"14_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_NART\" />\r              </specific>\r            </item>\r            <item top=\"21\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Art.Inventariable\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"26_U_Cb\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"26_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Art.Inventariable\" />\r            </item>\r            <item tab_order=\"3\" top=\"21\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Art.Inventariable\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"113\" visible=\"1\" uid=\"26_U_Cb\" IsAutoGenerated=\"1\">\r              <specific>\r                <ValidValues>\r                  <action type=\"add\" />\r                </ValidValues>\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_INVE\" />\r              </specific>\r            </item>\r            <item top=\"36\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Artículo servicio\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"27_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"27_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Artículo servicio\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"4\" text_style=\"0\" top=\"36\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Artículo servicio\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"27_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_ARTS\" />\r              </specific>\r            </item>\r            <item top=\"36\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Art.no inventariable\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"28_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"28_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Nombre Art.no inventariable\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"5\" text_style=\"0\" top=\"36\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Art.no inventariable\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"28_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_NARS\" />\r              </specific>\r            </item>\r            <item top=\"51\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Concepto de RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"16_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"16_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Concepto de RENTA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"6\" text_style=\"0\" top=\"51\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Concepto de RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"16_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_CRTA\" />\r              </specific>\r            </item>\r            <item top=\"51\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Indicador Retención Renta\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"22_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"22_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Indicador Retención Renta\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"7\" text_style=\"0\" top=\"51\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Indicador Retención Renta\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"22_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_IIRR\" />\r              </specific>\r            </item>\r            <item top=\"66\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret.Renta Abs\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"21_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"21_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Base Imponible Ret.Renta Abs\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"8\" text_style=\"0\" top=\"66\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret.Renta Abs\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"21_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_BIRA\" />\r              </specific>\r            </item>\r            <item top=\"66\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret.RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"19_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"19_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Base Imponible Ret.RENTA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"9\" text_style=\"0\" top=\"66\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret.RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"19_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_BIRR\" />\r              </specific>\r            </item>\r            <item top=\"81\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"% Retención RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"18_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"18_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"% Retención RENTA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"10\" text_style=\"0\" top=\"81\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"% Retención RENTA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"18_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_PRTA\" />\r              </specific>\r            </item>\r            <item top=\"81\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Concepto de IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"15_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"15_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Concepto de IVA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"11\" text_style=\"0\" top=\"81\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Concepto de IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"15_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_CIVA\" />\r              </specific>\r            </item>\r            <item top=\"96\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Indicador Retención IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"17_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"17_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Indicador Retención IVA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"12\" text_style=\"0\" top=\"96\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Indicador Retención IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"17_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_IIRI\" />\r              </specific>\r            </item>\r            <item top=\"96\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"% Retención IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"23_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"23_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"% Retención IVA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"13\" text_style=\"0\" top=\"96\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"% Retención IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"23_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_PRIV\" />\r              </specific>\r            </item>\r            <item top=\"111\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret. IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"24_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"24_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Base Imponible Ret. IVA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"14\" text_style=\"0\" top=\"111\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret. IVA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"24_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_BIRI\" />\r              </specific>\r            </item>\r            <item top=\"111\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret. IVA Abs.\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"25_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"25_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Base Imponible Ret. IVA Abs.\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"15\" text_style=\"0\" top=\"111\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Base Imponible Ret. IVA Abs.\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"25_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_BIIA\" />\r              </specific>\r            </item>\r            <item top=\"126\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"% Ret. Renta TESAKA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"29_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"29_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"% Ret. Renta TESAKA\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"16\" text_style=\"0\" top=\"126\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"% Ret. Renta TESAKA\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"29_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_ARIR\" alias=\"U_RTSK\" />\r              </specific>\r            </item>\r            <item top=\"195\" left=\"6\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"1\" IsAutoGenerated=\"1\">\r              <specific caption=\"Ok\" />\r            </item>\r            <item top=\"195\" left=\"76\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"2\" IsAutoGenerated=\"1\">\r              <specific caption=\"Cancel\" />\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection>\r          <action type=\"add\">\r            <ChooseFromList UniqueID=\"-1\" ObjectType=\"-1\" MultiSelection=\"0\" IsSystem=\"1\" />\r          </action>\r        </ChooseFromListCollection>\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings MatrixUID=\"\" Enabled=\"1\" EnableRowFormat=\"1\" />\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": null, "Code": "EPY_ARIR"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "Code", "FormColumnDescription": "Code", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_CART", "FormColumnDescription": "<PERSON><PERSON><PERSON>", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_NART", "FormColumnDescription": "Nombre Artículo", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_INVE", "FormColumnDescription": "Art.Inventariable", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_ARTS", "FormColumnDescription": "<PERSON><PERSON><PERSON><PERSON> servicio", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_NARS", "FormColumnDescription": "Nombre Art.no inventariable", "FormColumnNumber": 6, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_CRTA", "FormColumnDescription": "Concepto de RENTA", "FormColumnNumber": 7, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_IIRR", "FormColumnDescription": "Indicador Retención Renta", "FormColumnNumber": 8, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_BIRA", "FormColumnDescription": "Base Imponible Ret.Renta Abs", "FormColumnNumber": 9, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_BIRR", "FormColumnDescription": "Base Imponible Ret.RENTA", "FormColumnNumber": 10, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_PRTA", "FormColumnDescription": "% Retención RENTA", "FormColumnNumber": 11, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_CIVA", "FormColumnDescription": "Concepto de IVA", "FormColumnNumber": 12, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_IIRI", "FormColumnDescription": "Indicador Retención IVA", "FormColumnNumber": 13, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_PRIV", "FormColumnDescription": "% Retención IVA", "FormColumnNumber": 14, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_BIRI", "FormColumnDescription": "Base Imponible Ret. IVA", "FormColumnNumber": 15, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_BIIA", "FormColumnDescription": "Base Imponible Ret. IVA Abs.", "FormColumnNumber": 16, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}, {"FormColumnAlias": "U_RTSK", "FormColumnDescription": "% Ret. Renta TESAKA", "FormColumnNumber": 17, "SonNumber": 0, "Code": "EPY_ARIR", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_HCOM", "Code": "EPY_HCOM", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "<PERSON><PERSON>", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_HCOM", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"UTF-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"UDO_FT_EPY_HCOM\" FormType=\"UDO_FT_EPY_HCOM\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_HCOM\" title=\"Datos Hechauka Compras\" visible=\"1\" default_button=\"\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"407\" client_width=\"\" client_height=\"\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_HCOM\" mode=\"3\">\r        <datasources>\r          <DataTables />\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_HCOM\" />\r              <datasource tablename=\"@EPY_HTOP\" />\r              <datasource tablename=\"@EPY_HDOC\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"2\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"U_RC\" type=\"100\" left=\"5\" tab_order=\"0\" width=\"570\" top=\"122\" height=\"207\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"0\" right_just=\"0\" description=\"\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\">\r              <AutoManagedAttribute />\r              <specific />\r            </item>\r            <item top=\"103\" left=\"5\" width=\"100\" height=\"20\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"99\" visible=\"1\" uid=\"0_U_FD\" IsAutoGenerated=\"1\">\r              <specific pane=\"1\" caption=\"Maestro de Tipo de Operaciones\" AutoPaneSelection=\"1\">\r                <databind databound=\"1\" table=\"\" alias=\"FolderDS\" />\r              </specific>\r            </item>\r            <item top=\"103\" left=\"105\" width=\"100\" height=\"20\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"99\" visible=\"1\" uid=\"1_U_FD\" IsAutoGenerated=\"1\">\r              <specific pane=\"2\" caption=\"Maestro de Documentos\" AutoPaneSelection=\"1\">\r                <databind databound=\"1\" table=\"\" alias=\"FolderDS\" />\r              </specific>\r            </item>\r            <item top=\"6\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Code\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"0_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"0_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Code\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"33\" text_style=\"0\" top=\"6\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Code\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"0_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"Code\" />\r              </specific>\r            </item>\r            <item top=\"6\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Name\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"1_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"1_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Name\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"34\" text_style=\"0\" top=\"6\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Name\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"1_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"Name\" />\r              </specific>\r            </item>\r            <item top=\"21\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Obligación\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"13_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"13_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Código Obligación\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"35\" text_style=\"0\" top=\"21\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Obligación\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"13_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"U_COBC\" />\r              </specific>\r            </item>\r            <item top=\"21\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Formulario\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"14_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"14_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Código Formulario\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"36\" text_style=\"0\" top=\"21\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Formulario\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"14_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"U_CFOC\" />\r              </specific>\r            </item>\r            <item top=\"36\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Versión\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"15_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"15_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Versión\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"37\" text_style=\"0\" top=\"36\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Versión\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"15_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"U_VERC\" />\r              </specific>\r            </item>\r            <item top=\"36\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Proveedor del Exterior\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"16_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"16_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Proveedor del Exterior\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"38\" text_style=\"0\" top=\"36\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Proveedor del Exterior\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"16_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"U_PROC\" />\r              </specific>\r            </item>\r            <item top=\"51\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Retención Absorbida\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"17_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"17_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Retención Absorbida\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"39\" text_style=\"0\" top=\"51\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Retención Absorbida\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"17_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_HCOM\" alias=\"U_RTAC\" />\r              </specific>\r            </item>\r            <item top=\"347\" left=\"6\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"1\" IsAutoGenerated=\"1\">\r              <specific caption=\"OK\" />\r            </item>\r            <item top=\"347\" left=\"76\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"2\" IsAutoGenerated=\"1\">\r              <specific caption=\"Cancelar\" />\r            </item>\r            <item cellHeight=\"16\" tab_order=\"0\" titleHeight=\"21\" top=\"148\" left=\"15\" width=\"555\" height=\"163\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"1\" to_pane=\"1\" linkto=\"\" right_just=\"0\" type=\"127\" visible=\"1\" uid=\"0_U_G\" IsAutoGenerated=\"1\">\r              <specific layout=\"0\" SelectionMode=\"2\">\r                <columns>\r                  <action type=\"add\">\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"1\" val_off=\"N\" description=\"#\" title=\"#\" width=\"20\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"#\" sortable=\"0\" />\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"1\" val_off=\"N\" description=\"LineId\" title=\"LineId\" width=\"100\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"C_0_1\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HTOP\" alias=\"LineId\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"1\" val_off=\"N\" description=\"Código Tipo Operación\" title=\"Código Tipo Operación\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_2\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HTOP\" alias=\"U_CTOC\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"1\" val_off=\"N\" description=\"Nombre Tipo Operación\" title=\"Nombre Tipo Operación\" width=\"356\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_3\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HTOP\" alias=\"U_NTOC\" />\r                    </column>\r                  </action>\r                </columns>\r              </specific>\r            </item>\r            <item cellHeight=\"16\" tab_order=\"0\" titleHeight=\"21\" top=\"148\" left=\"15\" width=\"555\" height=\"163\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"2\" to_pane=\"2\" linkto=\"\" right_just=\"0\" type=\"127\" visible=\"1\" uid=\"1_U_G\" IsAutoGenerated=\"1\">\r              <specific layout=\"0\" SelectionMode=\"2\">\r                <columns>\r                  <action type=\"add\">\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"#\" title=\"#\" width=\"20\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"#\" sortable=\"0\" />\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"LineId\" title=\"LineId\" width=\"100\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"C_1_1\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"LineId\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Código Documento\" title=\"Código Documento\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_1_2\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_CDOC\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Nombre Documento\" title=\"Nombre Documento\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_1_3\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_NDOC\" />\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Informado en Hechauka Compras\" title=\"Informado en Hechauka Compras\" width=\"100\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"C_1_4\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_DIHC\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Informado en el Libro Compras\" title=\"Informado en el Libro Compras\" width=\"156\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"C_1_5\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_DILC\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" title=\"CODIGO\" width=\"50\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"Col_0\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_CDEQ\" />\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" title=\"Ventas\" width=\"50\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"Col_1\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_HDVT\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" title=\"Compras\" width=\"50\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"Col_2\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_HDCP\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" title=\"Ingresos\" width=\"50\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"Col_3\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_HDIG\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" title=\"Engresos\" width=\"50\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"Col_4\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_HDOC\" alias=\"U_HDEG\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                  </action>\r                </columns>\r              </specific>\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection>\r          <action type=\"add\">\r            <ChooseFromList UniqueID=\"-1\" ObjectType=\"-1\" MultiSelection=\"0\" IsSystem=\"1\" />\r          </action>\r        </ChooseFromListCollection>\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings MatrixUID=\"\" Enabled=\"1\" EnableRowFormat=\"1\" />\r        <items>\r          <action type=\"group\">\r            <item uid=\"0_U_FD\" />\r            <item uid=\"1_U_FD\" />\r          </action>\r        </items>\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_HTOP", "LogTableName": null, "Code": "EPY_HCOM", "ObjectName": "EPY_HTOP"}, {"SonNumber": 2, "TableName": "EPY_HDOC", "LogTableName": null, "Code": "EPY_HCOM", "ObjectName": "EPY_HDOC"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_HCOM"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_HCOM"}, {"ColumnNumber": 3, "ColumnAlias": "U_COBC", "ColumnDescription": "Código <PERSON>", "Code": "EPY_HCOM"}, {"ColumnNumber": 4, "ColumnAlias": "U_CFOC", "ColumnDescription": "Código <PERSON>", "Code": "EPY_HCOM"}, {"ColumnNumber": 5, "ColumnAlias": "U_VERC", "ColumnDescription": "Versión", "Code": "EPY_HCOM"}, {"ColumnNumber": 6, "ColumnAlias": "U_PROC", "ColumnDescription": "Proveedor del Exterior", "Code": "EPY_HCOM"}, {"ColumnNumber": 7, "ColumnAlias": "U_RTAC", "ColumnDescription": "Retención Absorbida", "Code": "EPY_HCOM"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "Code", "FormColumnDescription": "Code", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "Name", "FormColumnDescription": "Name", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "U_COBC", "FormColumnDescription": "Código <PERSON>", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "U_CFOC", "FormColumnDescription": "Código <PERSON>", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "U_VERC", "FormColumnDescription": "Versión", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "U_PROC", "FormColumnDescription": "Proveedor del Exterior", "FormColumnNumber": 6, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}, {"FormColumnAlias": "U_RTAC", "FormColumnDescription": "Retención Absorbida", "FormColumnNumber": 7, "SonNumber": 0, "Code": "EPY_HCOM", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_HCOM", "ColumnNumber": 1, "ChildNumber": 1, "ColumnAlias": "U_CTOC", "ColumnDescription": "Código Tipo Operación", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 2, "ChildNumber": 1, "ColumnAlias": "U_NTOC", "ColumnDescription": "Nombre Tipo Operación", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 3, "ChildNumber": 2, "ColumnAlias": "U_CDOC", "ColumnDescription": "Código <PERSON>nto", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 4, "ChildNumber": 2, "ColumnAlias": "U_NDOC", "ColumnDescription": "Nombre Documento", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 5, "ChildNumber": 2, "ColumnAlias": "U_DIHC", "ColumnDescription": "Informado en Hechauka Compras", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 6, "ChildNumber": 2, "ColumnAlias": "U_DILC", "ColumnDescription": "Informado en el Libro Compras", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 7, "ChildNumber": 2, "ColumnAlias": "U_CDEQ", "ColumnDescription": "CODIGO", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 8, "ChildNumber": 2, "ColumnAlias": "U_HDVT", "ColumnDescription": "Ventas", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 9, "ChildNumber": 2, "ColumnAlias": "U_HDCP", "ColumnDescription": "Compras", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 10, "ChildNumber": 2, "ColumnAlias": "U_HDIG", "ColumnDescription": "Ingresos", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_HCOM", "ColumnNumber": 11, "ChildNumber": 2, "ColumnAlias": "U_HDEG", "ColumnDescription": "Engresos", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_ABS_H", "Code": "EPY_ABS", "LogTableName": null, "CanCreateDefaultForm": "tNO", "ObjectType": "boud_Document", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tNO", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Absorción Retenciones Prov Ext.", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_ABS", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tYES", "FormSRF": null, "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_ABS_L", "LogTableName": null, "Code": "EPY_ABS", "ObjectName": "EPY_ABS_L"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "DocNum", "ColumnDescription": "DocNum", "Code": "EPY_ABS"}, {"ColumnNumber": 2, "ColumnAlias": "CreateDate", "ColumnDescription": "CreateDate", "Code": "EPY_ABS"}, {"ColumnNumber": 3, "ColumnAlias": "UpdateDate", "ColumnDescription": "UpdateDate", "Code": "EPY_ABS"}, {"ColumnNumber": 4, "ColumnAlias": "Creator", "ColumnDescription": "Creator", "Code": "EPY_ABS"}], "UserObjectMD_FormColumns": [], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_HVTA", "Code": "EPY_HVTA", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "<PERSON><PERSON>", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_HVTA", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_HVTA\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_HVTA\" title=\"Datos Hechauka Ventas\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"180\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_HVTA\">\r        <datasources>\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_HVTA\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"10\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Code\" />\r            </item>\r            <item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"40\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"Code\" />\r              </specific>\r            </item>\r            <item uid=\"13_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Obligación\" linkto=\"13_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Código Obligación\" />\r            </item>\r            <item uid=\"13_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Obligación\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"41\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_COBV\" />\r              </specific>\r            </item>\r            <item uid=\"14_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Formulario\" linkto=\"14_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Código Formulario\" />\r            </item>\r            <item uid=\"14_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Formulario\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"42\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_CFOV\" />\r              </specific>\r            </item>\r            <item uid=\"15_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Versión\" linkto=\"15_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Versión\" />\r            </item>\r            <item uid=\"15_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Versión\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"43\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_VERV\" />\r              </specific>\r            </item>\r            <item uid=\"16_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Consumidor Final\" linkto=\"16_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Consumidor Final\" />\r            </item>\r            <item uid=\"16_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Consumidor Final\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"44\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_COFV\" />\r              </specific>\r            </item>\r            <item uid=\"17_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clientes de Exportación\" linkto=\"17_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Clientes de Exportación\" />\r            </item>\r            <item uid=\"17_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clientes de Exportación\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"45\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_CEPV\" />\r              </specific>\r            </item>\r            <item uid=\"18_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"51\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Agentes Diplomáticos\" linkto=\"18_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Agentes Diplomáticos\" />\r            </item>\r            <item uid=\"18_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"51\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Agentes Diplomáticos\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"46\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_AGDV\" />\r              </specific>\r            </item>\r            <item uid=\"19_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"51\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clientes del Exterior\" linkto=\"19_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Clientes del Exterior\" />\r            </item>\r            <item uid=\"19_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"51\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clientes del Exterior\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"47\">\r                <databind databound=\"1\" table=\"@EPY_HVTA\" alias=\"U_CEXV\" />\r              </specific>\r            </item>\r            <item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"120\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"OK\" />\r            </item>\r            <item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"120\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Cancelar\" />\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection />\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings Enabled=\"1\" EnableRowFormat=\"1\" />\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_HVTA"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_HVTA"}, {"ColumnNumber": 3, "ColumnAlias": "U_COBV", "ColumnDescription": "Código <PERSON>", "Code": "EPY_HVTA"}, {"ColumnNumber": 4, "ColumnAlias": "U_CFOV", "ColumnDescription": "Código <PERSON>", "Code": "EPY_HVTA"}, {"ColumnNumber": 5, "ColumnAlias": "U_VERV", "ColumnDescription": "Versión", "Code": "EPY_HVTA"}, {"ColumnNumber": 6, "ColumnAlias": "U_COFV", "ColumnDescription": "Consumidor Final", "Code": "EPY_HVTA"}, {"ColumnNumber": 7, "ColumnAlias": "U_CEPV", "ColumnDescription": "Clientes de Exportación", "Code": "EPY_HVTA"}, {"ColumnNumber": 8, "ColumnAlias": "U_AGDV", "ColumnDescription": "<PERSON><PERSON>", "Code": "EPY_HVTA"}, {"ColumnNumber": 9, "ColumnAlias": "U_CEXV", "ColumnDescription": "Clientes del Exterior", "Code": "EPY_HVTA"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "Code", "FormColumnDescription": "Code", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "Name", "FormColumnDescription": "Name", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_COBV", "FormColumnDescription": "Código <PERSON>", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_CFOV", "FormColumnDescription": "Código <PERSON>", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_VERV", "FormColumnDescription": "Versión", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_COFV", "FormColumnDescription": "Consumidor Final", "FormColumnNumber": 6, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_CEPV", "FormColumnDescription": "Clientes de Exportación", "FormColumnNumber": 7, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_AGDV", "FormColumnDescription": "<PERSON><PERSON>", "FormColumnNumber": 8, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}, {"FormColumnAlias": "U_CEXV", "FormColumnDescription": "Clientes del Exterior", "FormColumnNumber": 9, "SonNumber": 0, "Code": "EPY_HVTA", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": []}, {"TableName": "EPY_CAMI_CAB", "Code": "EPY_CAMI_CAB", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "EPY_CAMI_CAB", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": null, "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?><Application><forms><action type=\"add\"><form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_CAMI_CAB\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_CAMI_CAB\" title=\"EPY_CAMI_CAB\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"428\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_CAMI_CAB\"><datasources><dbdatasources><action type=\"add\"><datasource tablename=\"@EPY_CAMI_CAB\" /><datasource tablename=\"@EPY_CAMI_DET\" /><datasource tablename=\"@EPY_CAMI_CACO\" /></action></dbdatasources><userdatasources><action type=\"add\"><datasource uid=\"FolderDS\" type=\"9\" size=\"10\" /></action></userdatasources></datasources><Menus /><items><action type=\"add\"><item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Code\" /></item><item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"0\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"Code\" /></specific></item><item uid=\"1_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Name\" linkto=\"1_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Name\" /></item><item uid=\"1_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Name\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"Name\" /></specific></item><item uid=\"13_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Cantidad precintos\" linkto=\"13_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Cantidad precintos\" /></item><item uid=\"13_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Cantidad precintos\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"2\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_CPRE\" /></specific></item><item uid=\"14_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Codigo tipo camion\" linkto=\"14_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Codigo tipo camion\" /></item><item uid=\"14_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Codigo tipo camion\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"3\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_CTCA\" /></specific></item><item uid=\"15_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Numero de chapa 1\" linkto=\"15_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Numero de chapa 1\" /></item><item uid=\"15_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Numero de chapa 1\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"4\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_CHA1\" /></specific></item><item uid=\"16_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Numero de chapa 2\" linkto=\"16_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Numero de chapa 2\" /></item><item uid=\"16_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Numero de chapa 2\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"5\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_CHA2\" /></specific></item><item uid=\"17_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"51\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Estado del camion\" linkto=\"17_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Estado del camion\" /></item><item uid=\"17_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"51\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Estado del camion\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"6\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_CEST\" /></specific></item><item uid=\"18_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"51\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Observaciones\" linkto=\"18_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Observaciones\" /></item><item uid=\"18_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"51\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Observaciones\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"7\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_C_OBS\" /></specific></item><item uid=\"19_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"66\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Marca\" linkto=\"19_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Marca\" /></item><item uid=\"19_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"66\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Marca\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"8\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_MARC\" /></specific></item><item uid=\"20_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"66\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Emblema\" linkto=\"20_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Emblema\" /></item><item uid=\"20_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"66\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Emblema\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific TabOrder=\"9\"><databind databound=\"1\" table=\"@EPY_CAMI_CAB\" alias=\"U_EMBL\" /></specific></item><item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"362\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"OK\" /></item><item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"362\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Cancelar\" /></item><item uid=\"0_U_FD\" type=\"99\" left=\"6\" tab_order=\"\" width=\"116\" top=\"118\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Detalle Camiones\" AffectsFormMode=\"1\" val_on=\"Y\" val_off=\"N\" pane=\"1\" AutoPaneSelection=\"1\"><databind databound=\"1\" table=\"\" alias=\"FolderDS\" /></specific></item><item uid=\"1_U_FD\" type=\"99\" left=\"122\" tab_order=\"\" width=\"116\" top=\"118\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific caption=\"Compartimiento Camiones\" AffectsFormMode=\"1\" val_on=\"Y\" val_off=\"N\" pane=\"2\" AutoPaneSelection=\"1\"><databind databound=\"1\" table=\"\" alias=\"FolderDS\" /></specific></item><item uid=\"U_RC\" type=\"100\" left=\"5\" tab_order=\"\" width=\"570\" top=\"136\" height=\"207\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /></item><item uid=\"0_U_G\" type=\"127\" left=\"15\" tab_order=\"\" width=\"555\" top=\"163\" height=\"163\" visible=\"1\" enabled=\"1\" from_pane=\"1\" to_pane=\"1\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific SelectionMode=\"2\"><columns><action type=\"add\"><column uid=\"#\" type=\"16\" title=\"#\" description=\"#\" visible=\"1\" AffectsFormMode=\"1\" width=\"20\" disp_desc=\"0\" editable=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\" /><column uid=\"C_0_1\" type=\"16\" title=\"Código requisito\" description=\"Código requisito\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_DET\" alias=\"U_CREQ\" /></column><column uid=\"C_0_2\" type=\"16\" title=\"Nombre requisito\" description=\"Nombre requisito\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_DET\" alias=\"U_NREQ\" /></column><column uid=\"C_0_3\" type=\"16\" title=\"Tipo requisito\" description=\"Tipo requisito\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_DET\" alias=\"U_TREQ\" /></column><column uid=\"C_0_4\" type=\"16\" title=\"Observaciones\" description=\"Observaciones\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_DET\" alias=\"U_COBS\" /></column><column uid=\"C_0_5\" type=\"16\" title=\"Fecha vencimiento\" description=\"Fecha vencimiento\" visible=\"1\" AffectsFormMode=\"1\" width=\"156\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_DET\" alias=\"U_FECV\" /></column></action></columns></specific></item><item uid=\"1_U_G\" type=\"127\" left=\"15\" tab_order=\"\" width=\"555\" top=\"163\" height=\"163\" visible=\"1\" enabled=\"1\" from_pane=\"2\" to_pane=\"2\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\"><AutoManagedAttribute /><specific SelectionMode=\"2\"><columns><action type=\"add\"><column uid=\"#\" type=\"16\" title=\"#\" description=\"#\" visible=\"1\" AffectsFormMode=\"1\" width=\"20\" disp_desc=\"0\" editable=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\" /><column uid=\"C_1_1\" type=\"16\" title=\"Numero compartimento\" description=\"Numero compartimento\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_CACO\" alias=\"U_NCOM\" /></column><column uid=\"C_1_2\" type=\"16\" title=\"Capacidad total\" description=\"Capacidad total\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_CACO\" alias=\"U_CAPT\" /></column><column uid=\"C_1_3\" type=\"16\" title=\"Cantidad precintos\" description=\"Cantidad precintos\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_CACO\" alias=\"U_CANP\" /></column><column uid=\"C_1_4\" type=\"16\" title=\"Cantidad botellas\" description=\"Cantidad botellas\" visible=\"1\" AffectsFormMode=\"1\" width=\"256\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\"><databind databound=\"1\" table=\"@EPY_CAMI_CACO\" alias=\"U_CANB\" /></column></action></columns></specific></item></action></items><ChooseFromListCollection /><DataBrowser BrowseBy=\"0_U_E\" /><Settings Enabled=\"1\" EnableRowFormat=\"1\" /><items><action type=\"group\"><item uid=\"0_U_FD\" IsAutoGenerated=\"0\" /><item uid=\"1_U_FD\" IsAutoGenerated=\"0\" /></action></items></form></action></forms></Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_CAMI_DET", "LogTableName": null, "Code": "EPY_CAMI_CAB", "ObjectName": "EPY_CAMI_DET"}, {"SonNumber": 2, "TableName": "EPY_CAMI_CACO", "LogTableName": null, "Code": "EPY_CAMI_CAB", "ObjectName": "EPY_CAMI_CACO"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 3, "ColumnAlias": "U_CPRE", "ColumnDescription": "Cantidad precintos", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 4, "ColumnAlias": "U_CTCA", "ColumnDescription": "Codigo tipo camion", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 5, "ColumnAlias": "U_CHA1", "ColumnDescription": "Numero de chapa 1", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 6, "ColumnAlias": "U_CHA2", "ColumnDescription": "Numero de chapa 2", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 7, "ColumnAlias": "U_CEST", "ColumnDescription": "Estado del camion", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 8, "ColumnAlias": "U_C_OBS", "ColumnDescription": "Observaciones", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 9, "ColumnAlias": "U_MARC", "ColumnDescription": "<PERSON><PERSON>", "Code": "EPY_CAMI_CAB"}, {"ColumnNumber": 10, "ColumnAlias": "U_EMBL", "ColumnDescription": "<PERSON><PERSON><PERSON>", "Code": "EPY_CAMI_CAB"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "U_NCOM", "FormColumnDescription": "Numero compartimento", "FormColumnNumber": 1, "SonNumber": 2, "Code": "EPY_CAMI_CAB", "Editable": "tNO"}, {"FormColumnAlias": "U_CAPT", "FormColumnDescription": "Capacidad total", "FormColumnNumber": 2, "SonNumber": 2, "Code": "EPY_CAMI_CAB", "Editable": "tNO"}, {"FormColumnAlias": "U_CANP", "FormColumnDescription": "Cantidad precintos", "FormColumnNumber": 3, "SonNumber": 2, "Code": "EPY_CAMI_CAB", "Editable": "tNO"}, {"FormColumnAlias": "U_CANB", "FormColumnDescription": "Cantidad botellas", "FormColumnNumber": 4, "SonNumber": 2, "Code": "EPY_CAMI_CAB", "Editable": "tNO"}, {"FormColumnAlias": "Code", "FormColumnDescription": "Code", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "Name", "FormColumnDescription": "Name", "FormColumnNumber": 6, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_CPRE", "FormColumnDescription": "Cantidad precintos", "FormColumnNumber": 7, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_CTCA", "FormColumnDescription": "Codigo tipo camion", "FormColumnNumber": 8, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_CHA1", "FormColumnDescription": "Numero de chapa 1", "FormColumnNumber": 9, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_CHA2", "FormColumnDescription": "Numero de chapa 2", "FormColumnNumber": 10, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_CEST", "FormColumnDescription": "Estado del camion", "FormColumnNumber": 11, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_C_OBS", "FormColumnDescription": "Observaciones", "FormColumnNumber": 12, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_MARC", "FormColumnDescription": "<PERSON><PERSON>", "FormColumnNumber": 13, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}, {"FormColumnAlias": "U_EMBL", "FormColumnDescription": "<PERSON><PERSON><PERSON>", "FormColumnNumber": 14, "SonNumber": 0, "Code": "EPY_CAMI_CAB", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_CAMI_CAB", "ColumnNumber": 1, "ChildNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 2, "ChildNumber": 1, "ColumnAlias": "LineId", "ColumnDescription": "LineId", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 3, "ChildNumber": 1, "ColumnAlias": "Object", "ColumnDescription": "Object", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 4, "ChildNumber": 1, "ColumnAlias": "LogInst", "ColumnDescription": "LogInst", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 5, "ChildNumber": 1, "ColumnAlias": "U_CREQ", "ColumnDescription": "<PERSON><PERSON><PERSON> requisito", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 6, "ChildNumber": 1, "ColumnAlias": "U_NREQ", "ColumnDescription": "Nombre requisito", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 7, "ChildNumber": 1, "ColumnAlias": "U_TREQ", "ColumnDescription": "<PERSON><PERSON><PERSON> requisito", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 8, "ChildNumber": 1, "ColumnAlias": "U_COBS", "ColumnDescription": "Observaciones", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 9, "ChildNumber": 1, "ColumnAlias": "U_FECV", "ColumnDescription": "<PERSON><PERSON>", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 1, "ChildNumber": 2, "ColumnAlias": "Code", "ColumnDescription": "Code", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 2, "ChildNumber": 2, "ColumnAlias": "LineId", "ColumnDescription": "LineId", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 3, "ChildNumber": 2, "ColumnAlias": "Object", "ColumnDescription": "Object", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 4, "ChildNumber": 2, "ColumnAlias": "LogInst", "ColumnDescription": "LogInst", "ColumnIsUsed": "tNO", "Editable": "tNO"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 5, "ChildNumber": 2, "ColumnAlias": "U_NCOM", "ColumnDescription": "Numero compartimento", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 6, "ChildNumber": 2, "ColumnAlias": "U_CAPT", "ColumnDescription": "Capacidad total", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 7, "ChildNumber": 2, "ColumnAlias": "U_CANP", "ColumnDescription": "Cantidad precintos", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CAMI_CAB", "ColumnNumber": 8, "ChildNumber": 2, "ColumnAlias": "U_CANB", "ColumnDescription": "Cantidad botellas", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_SFEU", "Code": "EPY_SFEU", "LogTableName": "AEPY_SFEU", "CanCreateDefaultForm": "tYES", "ObjectType": "boud_Document", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Usuarios por Sitio de Facturación", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_SFEU", "EnableEnhancedForm": "tNO", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r<Application>\r\t<forms>\r\t\t<action type=\"add\">\r\t\t\t<form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_SFEU\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_SFEU\" title=\"Usuarios por Sitio de Facturación\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"165\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_SFEU\">\r\t\t\t\t<datasources>\r\t\t\t\t\t<dbdatasources>\r\t\t\t\t\t\t<action type=\"add\">\r\t\t\t\t\t\t\t<datasource tablename=\"@EPY_SFEU\" />\r\t\t\t\t\t\t</action>\r\t\t\t\t\t</dbdatasources>\r\t\t\t\t\t<userdatasources>\r\t\t\t\t\t\t<action type=\"add\">\r\t\t\t\t\t\t\t<datasource uid=\"FolderDS\" type=\"9\" size=\"10\" />\r\t\t\t\t\t\t</action>\r\t\t\t\t\t</userdatasources>\r\t\t\t\t</datasources>\r\t\t\t\t<Menus />\r\t\t\t\t<items>\r\t\t\t\t\t<action type=\"add\">\r\t\t\t\t\t\t<item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"DocEntry\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"0\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific TabOrder=\"66\">\r\t\t\t\t\t\t\t\t<databind databound=\"1\" table=\"@EPY_SFEU\" alias=\"DocEntry\" />\r\t\t\t\t\t\t\t</specific>\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"20_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Sitio de Facturacion\" linkto=\"20_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"Sitio de Facturacion\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"20_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Sitio de Facturacion\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific TabOrder=\"67\">\r\t\t\t\t\t\t\t\t<databind databound=\"1\" table=\"@EPY_SFEU\" alias=\"U_CSSF\" />\r\t\t\t\t\t\t\t</specific>\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"21_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Codigo Usuario\" linkto=\"21_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"Codigo Usuario\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"21_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Codigo Usuario\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific TabOrder=\"68\">\r\t\t\t\t\t\t\t\t<databind databound=\"1\" table=\"@EPY_SFEU\" alias=\"U_CUSF\" />\r\t\t\t\t\t\t\t</specific>\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"22_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Nombre Usuario\" linkto=\"22_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"Nombre Usuario\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"22_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Nombre Usuario\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific TabOrder=\"69\">\r\t\t\t\t\t\t\t\t<databind databound=\"1\" table=\"@EPY_SFEU\" alias=\"U_NUSF\" />\r\t\t\t\t\t\t\t</specific>\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"23_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Sitio estandar del usuario\" linkto=\"23_U_Cb\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"Sitio estandar del usuario\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"23_U_Cb\" type=\"113\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Sitio estandar del usuario\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific TabOrder=\"70\">\r\t\t\t\t\t\t\t\t<databind databound=\"1\" table=\"@EPY_SFEU\" alias=\"U_EUSF\" />\r\t\t\t\t\t\t\t</specific>\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"105\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"OK\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t\t<item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"105\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r\t\t\t\t\t\t\t<AutoManagedAttribute />\r\t\t\t\t\t\t\t<specific caption=\"Cancelar\" />\r\t\t\t\t\t\t</item>\r\t\t\t\t\t</action>\r\t\t\t\t</items>\r\t\t\t\t<ChooseFromListCollection />\r\t\t\t\t<DataBrowser BrowseBy=\"0_U_E\" />\r\t\t\t\t<Settings Enabled=\"1\" EnableRowFormat=\"1\" />\r\t\t\t</form>\r\t\t</action>\r\t</forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "DocNum", "ColumnDescription": "DocNum", "Code": "EPY_SFEU"}, {"ColumnNumber": 2, "ColumnAlias": "CreateDate", "ColumnDescription": "CreateDate", "Code": "EPY_SFEU"}, {"ColumnNumber": 3, "ColumnAlias": "UpdateDate", "ColumnDescription": "UpdateDate", "Code": "EPY_SFEU"}, {"ColumnNumber": 4, "ColumnAlias": "U_CSSF", "ColumnDescription": "Sitio Facturacion", "Code": "EPY_SFEU"}, {"ColumnNumber": 5, "ColumnAlias": "U_CUSF", "ColumnDescription": "Codigo Usuario", "Code": "EPY_SFEU"}, {"ColumnNumber": 6, "ColumnAlias": "U_NUSF", "ColumnDescription": "Nombre Usuario", "Code": "EPY_SFEU"}, {"ColumnNumber": 7, "ColumnAlias": "U_EUSF", "ColumnDescription": "Sitio estandar del usuario", "Code": "EPY_SFEU"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "DocEntry", "FormColumnDescription": "DocEntry", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_SFEU", "Editable": "tNO"}, {"FormColumnAlias": "U_CSSF", "FormColumnDescription": "Sitio de Facturacion", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_SFEU", "Editable": "tYES"}, {"FormColumnAlias": "U_CUSF", "FormColumnDescription": "Codigo Usuario", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_SFEU", "Editable": "tYES"}, {"FormColumnAlias": "U_NUSF", "FormColumnDescription": "Nombre Usuario", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_SFEU", "Editable": "tYES"}, {"FormColumnAlias": "U_EUSF", "FormColumnDescription": "Sitio estandar del usuario", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_SFEU", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_SFEU", "ColumnNumber": 1, "ChildNumber": 0, "ColumnAlias": "DocEntry", "ColumnDescription": "DocEntry", "ColumnIsUsed": "tYES", "Editable": "tNO"}, {"Code": "EPY_SFEU", "ColumnNumber": 2, "ChildNumber": 0, "ColumnAlias": "U_CSSF", "ColumnDescription": "Sitio de Facturacion", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_SFEU", "ColumnNumber": 3, "ChildNumber": 0, "ColumnAlias": "U_CUSF", "ColumnDescription": "Codigo Usuario", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_SFEU", "ColumnNumber": 4, "ChildNumber": 0, "ColumnAlias": "U_NUSF", "ColumnDescription": "Nombre Usuario", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_SFEU", "ColumnNumber": 5, "ChildNumber": 0, "ColumnAlias": "U_EUSF", "ColumnDescription": "Sitio estandar del usuario", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_TSNF", "Code": "EPY_TSNF", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_Document", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Tipo de Socio de Negocio Fiscal", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_TSNF", "EnableEnhancedForm": "tNO", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_TSNF\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_TSNF\" title=\"Tipo de Socio de Negocio Fiscal\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"150\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_TSNF\">\r        <datasources>\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_TSNF\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"10\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"DocEntry\" />\r            </item>\r            <item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"0\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"89\">\r                <databind databound=\"1\" table=\"@EPY_TSNF\" alias=\"DocEntry\" />\r              </specific>\r            </item>\r            <item uid=\"20_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clase Socio de Negocio\" linkto=\"20_U_Cb\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Clase Socio de Negocio\" />\r            </item>\r            <item uid=\"20_U_Cb\" type=\"113\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clase Socio de Negocio\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"90\">\r                <databind databound=\"1\" table=\"@EPY_TSNF\" alias=\"U_CASN\" />\r              </specific>\r            </item>\r            <item uid=\"21_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"RUC Generico SN\" linkto=\"21_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"RUC Generico SN\" />\r            </item>\r            <item uid=\"21_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"RUC Generico SN\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"91\">\r                <databind databound=\"1\" table=\"@EPY_TSNF\" alias=\"U_RGSN\" />\r              </specific>\r            </item>\r            <item uid=\"22_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clase de Socio de Negocio\" linkto=\"22_U_Cb\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Clase de Socio de Negocio\" />\r            </item>\r            <item uid=\"22_U_Cb\" type=\"113\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Clase de Socio de Negocio\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"92\">\r                <databind databound=\"1\" table=\"@EPY_TSNF\" alias=\"U_CSNF\" />\r              </specific>\r            </item>\r            <item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"90\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"OK\" />\r            </item>\r            <item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"90\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Cancelar\" />\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection />\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings Enabled=\"1\" EnableRowFormat=\"1\" />\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "DocEntry", "ColumnDescription": "DocEntry", "Code": "EPY_TSNF"}, {"ColumnNumber": 2, "ColumnAlias": "DocNum", "ColumnDescription": "DocNum", "Code": "EPY_TSNF"}, {"ColumnNumber": 3, "ColumnAlias": "CreateDate", "ColumnDescription": "CreateDate", "Code": "EPY_TSNF"}, {"ColumnNumber": 4, "ColumnAlias": "UpdateDate", "ColumnDescription": "UpdateDate", "Code": "EPY_TSNF"}, {"ColumnNumber": 5, "ColumnAlias": "U_CASN", "ColumnDescription": "Clase Socio de Negocio", "Code": "EPY_TSNF"}, {"ColumnNumber": 6, "ColumnAlias": "U_RGSN", "ColumnDescription": "RUC Generico SN", "Code": "EPY_TSNF"}, {"ColumnNumber": 7, "ColumnAlias": "U_CSNF", "ColumnDescription": "Clase de Socio de Negocio", "Code": "EPY_TSNF"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "DocEntry", "FormColumnDescription": "DocEntry", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_TSNF", "Editable": "tNO"}, {"FormColumnAlias": "U_CASN", "FormColumnDescription": "Clase Socio de Negocio", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_TSNF", "Editable": "tYES"}, {"FormColumnAlias": "U_RGSN", "FormColumnDescription": "RUC Generico SN", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_TSNF", "Editable": "tYES"}, {"FormColumnAlias": "U_CSNF", "FormColumnDescription": "Clase de Socio de Negocio", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_TSNF", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_TSNF", "ColumnNumber": 1, "ChildNumber": 0, "ColumnAlias": "DocEntry", "ColumnDescription": "DocEntry", "ColumnIsUsed": "tYES", "Editable": "tNO"}, {"Code": "EPY_TSNF", "ColumnNumber": 2, "ChildNumber": 0, "ColumnAlias": "U_CASN", "ColumnDescription": "Clase Socio de Negocio", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TSNF", "ColumnNumber": 3, "ChildNumber": 0, "ColumnAlias": "U_RGSN", "ColumnDescription": "RUC Generico SN", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TSNF", "ColumnNumber": 4, "ChildNumber": 0, "ColumnAlias": "U_CSNF", "ColumnDescription": "Clase de Socio de Negocio", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_EQDC", "Code": "EPY_EQDC", "LogTableName": null, "CanCreateDefaultForm": "tYES", "ObjectType": "boud_Document", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tNO", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "Maestro de Equivalencia de Documentos", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_EQDC", "EnableEnhancedForm": "tNO", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_EQDC\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_EQDC\" title=\"Maestro de Equivalencia de Documentos\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"165\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_EQDC\">\r        <datasources>\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_EQDC\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"10\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"DocEntry\" />\r            </item>\r            <item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"0\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"DocEntry\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"100\">\r                <databind databound=\"1\" table=\"@EPY_EQDC\" alias=\"DocEntry\" />\r              </specific>\r            </item>\r            <item uid=\"20_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Documento de SAP BO\" linkto=\"20_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Código Documento de SAP BO\" />\r            </item>\r            <item uid=\"20_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código Documento de SAP BO\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"101\">\r                <databind databound=\"1\" table=\"@EPY_EQDC\" alias=\"U_CDSB\" />\r              </specific>\r            </item>\r            <item uid=\"21_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código de Documento SubType\" linkto=\"21_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Código de Documento SubType\" />\r            </item>\r            <item uid=\"21_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código de Documento SubType\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"102\">\r                <databind databound=\"1\" table=\"@EPY_EQDC\" alias=\"U_CSTS\" />\r              </specific>\r            </item>\r            <item uid=\"22_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"21\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código de Documento Hechauka\" linkto=\"22_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Código de Documento Hechauka\" />\r            </item>\r            <item uid=\"22_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"21\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Código de Documento Hechauka\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"103\">\r                <databind databound=\"1\" table=\"@EPY_EQDC\" alias=\"U_CDOC\" />\r              </specific>\r            </item>\r            <item uid=\"23_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"36\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Descripción Documento\" linkto=\"23_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Descripción Documento\" />\r            </item>\r            <item uid=\"23_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"36\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Descripción Documento\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"104\">\r                <databind databound=\"1\" table=\"@EPY_EQDC\" alias=\"U_CDDD\" />\r              </specific>\r            </item>\r            <item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"105\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"OK\" />\r            </item>\r            <item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"105\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Cancelar\" />\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection />\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings Enabled=\"1\" EnableRowFormat=\"1\" />\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "DocEntry", "ColumnDescription": "DocEntry", "Code": "EPY_EQDC"}, {"ColumnNumber": 2, "ColumnAlias": "DocNum", "ColumnDescription": "DocNum", "Code": "EPY_EQDC"}, {"ColumnNumber": 3, "ColumnAlias": "CreateDate", "ColumnDescription": "CreateDate", "Code": "EPY_EQDC"}, {"ColumnNumber": 4, "ColumnAlias": "UpdateDate", "ColumnDescription": "UpdateDate", "Code": "EPY_EQDC"}, {"ColumnNumber": 5, "ColumnAlias": "U_CDSB", "ColumnDescription": "Código Documento de SAP BO", "Code": "EPY_EQDC"}, {"ColumnNumber": 6, "ColumnAlias": "U_CSTS", "ColumnDescription": "Código de Documento SubType", "Code": "EPY_EQDC"}, {"ColumnNumber": 7, "ColumnAlias": "U_CDOC", "ColumnDescription": "Código de Documento Hechauka", "Code": "EPY_EQDC"}, {"ColumnNumber": 8, "ColumnAlias": "U_CDDD", "ColumnDescription": "Descripción Documento", "Code": "EPY_EQDC"}, {"ColumnNumber": 9, "ColumnAlias": "U_CDDE", "ColumnDescription": "Codigo DE", "Code": "EPY_EQDC"}, {"ColumnNumber": 10, "ColumnAlias": "U_DDDE", "ColumnDescription": "Descripcion DE", "Code": "EPY_EQDC"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "DocEntry", "FormColumnDescription": "DocEntry", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tNO"}, {"FormColumnAlias": "U_CDSB", "FormColumnDescription": "Código Documento de SAP BO", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}, {"FormColumnAlias": "U_CSTS", "FormColumnDescription": "Código de Documento SubType", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}, {"FormColumnAlias": "U_CDOC", "FormColumnDescription": "Código de Documento Hechauka", "FormColumnNumber": 4, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}, {"FormColumnAlias": "U_CDDD", "FormColumnDescription": "Descripción Documento", "FormColumnNumber": 5, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}, {"FormColumnAlias": "U_CDDE", "FormColumnDescription": "Codigo DE", "FormColumnNumber": 6, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}, {"FormColumnAlias": "U_DDDE", "FormColumnDescription": "Descripcion DE", "FormColumnNumber": 7, "SonNumber": 0, "Code": "EPY_EQDC", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_EQDC", "ColumnNumber": 1, "ChildNumber": 0, "ColumnAlias": "DocEntry", "ColumnDescription": "DocEntry", "ColumnIsUsed": "tYES", "Editable": "tNO"}, {"Code": "EPY_EQDC", "ColumnNumber": 2, "ChildNumber": 0, "ColumnAlias": "U_CDSB", "ColumnDescription": "Código Documento de SAP BO", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_EQDC", "ColumnNumber": 3, "ChildNumber": 0, "ColumnAlias": "U_CSTS", "ColumnDescription": "Código de Documento SubType", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_EQDC", "ColumnNumber": 4, "ChildNumber": 0, "ColumnAlias": "U_CDOC", "ColumnDescription": "Código de Documento Hechauka", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_EQDC", "ColumnNumber": 5, "ChildNumber": 0, "ColumnAlias": "U_CDDD", "ColumnDescription": "Descripción Documento", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_EQDC", "ColumnNumber": 6, "ChildNumber": 0, "ColumnAlias": "U_CDDE", "ColumnDescription": "Codigo DE", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_EQDC", "ColumnNumber": 7, "ChildNumber": 0, "ColumnAlias": "U_DDDE", "ColumnDescription": "Descripcion DE", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_TPRC", "Code": "EPY_TPRC", "LogTableName": "AEPY_TPRC", "CanCreateDefaultForm": "tYES", "ObjectType": "boud_Document", "ExtensionName": null, "CanCancel": "tYES", "CanDelete": "tYES", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "EPY_TPRC", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_TPRC", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"UTF-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"UDO_FT_EPY_TPRC\" FormType=\"UDO_FT_EPY_TPRC\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_TPRC\" title=\"Control Timbrado de Proveedor\" visible=\"1\" default_button=\"\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"377\" client_width=\"\" client_height=\"\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_TPRC\" mode=\"3\">\r        <datasources>\r          <DataTables />\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_TPRC\" />\r              <datasource tablename=\"@EPY_TPRD\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"1\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"U_RC\" type=\"100\" left=\"5\" tab_order=\"0\" width=\"570\" top=\"92\" height=\"207\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"0\" right_just=\"0\" description=\"\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\">\r              <AutoManagedAttribute />\r              <specific />\r            </item>\r            <item top=\"73\" left=\"5\" width=\"100\" height=\"20\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"99\" visible=\"1\" uid=\"0_U_FD\" IsAutoGenerated=\"1\">\r              <specific pane=\"1\" caption=\"Timbrado Proveedores Det\" AutoPaneSelection=\"1\">\r                <databind databound=\"1\" table=\"\" alias=\"FolderDS\" />\r              </specific>\r            </item>\r            <item top=\"6\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"DocEntry\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"0_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"0_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"DocEntry\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"105\" text_style=\"0\" top=\"6\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"DocEntry\" disp_desc=\"0\" enabled=\"0\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"0_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_TPRC\" alias=\"DocEntry\" />\r              </specific>\r            </item>\r            <item top=\"6\" left=\"306\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Proveedor\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"20_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"20_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Código Proveedor\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"106\" text_style=\"0\" top=\"6\" left=\"427\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Código Proveedor\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"20_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_TPRC\" alias=\"U_CPTP\" />\r              </specific>\r            </item>\r            <item top=\"21\" left=\"6\" width=\"121\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Proveedor\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"21_U_E\" right_just=\"0\" type=\"8\" visible=\"1\" uid=\"21_U_S\" IsAutoGenerated=\"1\">\r              <specific caption=\"Nombre Proveedor\" />\r            </item>\r            <item backcolor=\"-1\" font_size=\"-1\" forecolor=\"-1\" tab_order=\"107\" text_style=\"0\" top=\"21\" left=\"127\" width=\"148\" height=\"14\" AffectsFormMode=\"1\" description=\"Nombre Proveedor\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"16\" visible=\"1\" uid=\"21_U_E\" IsAutoGenerated=\"1\">\r              <specific ChooseFromListAlias=\"\" ChooseFromListIsAutoFill=\"0\" ChooseFromListUID=\"\" IsPassword=\"0\" supp_zeros=\"0\">\r                <databind databound=\"1\" table=\"@EPY_TPRC\" alias=\"U_NPTP\" />\r              </specific>\r            </item>\r            <item top=\"317\" left=\"6\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"1\" IsAutoGenerated=\"1\">\r              <specific caption=\"OK\" />\r            </item>\r            <item top=\"317\" left=\"76\" width=\"65\" height=\"19\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" linkto=\"\" right_just=\"0\" type=\"4\" visible=\"1\" uid=\"2\" IsAutoGenerated=\"1\">\r              <specific caption=\"Cancelar\" />\r            </item>\r            <item cellHeight=\"16\" tab_order=\"0\" titleHeight=\"21\" top=\"118\" left=\"15\" width=\"555\" height=\"163\" AffectsFormMode=\"1\" description=\"\" disp_desc=\"0\" enabled=\"1\" from_pane=\"1\" to_pane=\"1\" linkto=\"\" right_just=\"0\" type=\"127\" visible=\"1\" uid=\"0_U_G\" IsAutoGenerated=\"1\">\r              <specific layout=\"0\" SelectionMode=\"2\">\r                <columns>\r                  <action type=\"add\">\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"#\" title=\"#\" width=\"20\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"#\" sortable=\"0\" />\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"LineId\" title=\"LineId\" width=\"100\" editable=\"0\" type=\"16\" right_just=\"0\" uid=\"C_0_1\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"LineId\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Tipo de Documento\" title=\"Tipo de Documento\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_2\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_TDTP\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"N° Timbrado Proveedor\" title=\"N° Timbrado Proveedor\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_3\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_NRTP\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Establecimiento\" title=\"Establecimiento\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_4\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_ETTP\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Punto de Emision\" title=\"Punto de Emision\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_5\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_PETP\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Fecha Inicio Vigencia\" title=\"Fecha Inicio Vigencia\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_6\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_FITP\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Fecha Vencimiento Timbrado\" title=\"Fecha Vencimiento Timbrado\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_7\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_FVTP\" />\r                    </column>\r                    <column disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Tipo de Comprobante\" title=\"Tipo de Comprobante\" width=\"100\" editable=\"1\" type=\"113\" right_just=\"0\" uid=\"C_0_7\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_TCOM\" />\r                      <ValidValues>\r                        <action type=\"add\" />\r                      </ValidValues>\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Tipo de Comprobante\" title=\"Numero Desde\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_8\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_NDES\" />\r                    </column>\r                    <column backcolor=\"-1\" ChooseFromListIsAutoFill=\"0\" font_size=\"-1\" forecolor=\"-1\" text_style=\"0\" disp_desc=\"0\" visible=\"1\" AffectsFormMode=\"1\" val_on=\"Y\" IsAutoGenerated=\"0\" val_off=\"N\" description=\"Tipo de Comprobante\" title=\"Numero Hasta\" width=\"100\" editable=\"1\" type=\"16\" right_just=\"0\" uid=\"C_0_9\" sortable=\"0\">\r                      <databind databound=\"1\" table=\"@EPY_TPRD\" alias=\"U_NHAS\" />\r                    </column>\r                  </action>\r                </columns>\r              </specific>\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection>\r          <action type=\"add\">\r            <ChooseFromList UniqueID=\"-1\" ObjectType=\"-1\" MultiSelection=\"0\" IsSystem=\"1\" />\r          </action>\r        </ChooseFromListCollection>\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings MatrixUID=\"\" Enabled=\"1\" EnableRowFormat=\"1\" />\r        <items>\r          <action type=\"group\">\r            <item uid=\"0_U_FD\" />\r          </action>\r        </items>\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_TPRD", "LogTableName": "AEPY_TPRD", "Code": "EPY_TPRC", "ObjectName": "EPY_TPRD"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "DocNum", "ColumnDescription": "DocNum", "Code": "EPY_TPRC"}, {"ColumnNumber": 2, "ColumnAlias": "CreateDate", "ColumnDescription": "CreateDate", "Code": "EPY_TPRC"}, {"ColumnNumber": 3, "ColumnAlias": "UpdateDate", "ColumnDescription": "UpdateDate", "Code": "EPY_TPRC"}, {"ColumnNumber": 4, "ColumnAlias": "U_CPTP", "ColumnDescription": "<PERSON><PERSON><PERSON>", "Code": "EPY_TPRC"}, {"ColumnNumber": 5, "ColumnAlias": "U_NPTP", "ColumnDescription": "Nombre Proveedor", "Code": "EPY_TPRC"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "DocEntry", "FormColumnDescription": "DocEntry", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_TPRC", "Editable": "tNO"}, {"FormColumnAlias": "U_CPTP", "FormColumnDescription": "<PERSON><PERSON><PERSON>", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_TPRC", "Editable": "tYES"}, {"FormColumnAlias": "U_NPTP", "FormColumnDescription": "Nombre Proveedor", "FormColumnNumber": 3, "SonNumber": 0, "Code": "EPY_TPRC", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_TPRC", "ColumnNumber": 1, "ChildNumber": 1, "ColumnAlias": "LineId", "ColumnDescription": "LineId", "ColumnIsUsed": "tYES", "Editable": "tNO"}, {"Code": "EPY_TPRC", "ColumnNumber": 2, "ChildNumber": 1, "ColumnAlias": "U_TDTP", "ColumnDescription": "Tipo de Documento", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 3, "ChildNumber": 1, "ColumnAlias": "U_NRTP", "ColumnDescription": "N° Timbrado Proveedor", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 4, "ChildNumber": 1, "ColumnAlias": "U_ETTP", "ColumnDescription": "Establecimiento", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 5, "ChildNumber": 1, "ColumnAlias": "U_PETP", "ColumnDescription": "Punto de Emision", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 6, "ChildNumber": 1, "ColumnAlias": "U_FVTP", "ColumnDescription": "<PERSON><PERSON>", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 7, "ChildNumber": 1, "ColumnAlias": "U_NDES", "ColumnDescription": "Numero Desde", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 8, "ChildNumber": 1, "ColumnAlias": "U_NHAS", "ColumnDescription": "Numero Hasta", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_TPRC", "ColumnNumber": 9, "ChildNumber": 1, "ColumnAlias": "U_TCOM", "ColumnDescription": "Tipo de Comprobante", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}, {"TableName": "EPY_CTCA", "Code": "EPY_CTCA", "LogTableName": "AEPY_CTCA", "CanCreateDefaultForm": "tYES", "ObjectType": "boud_MasterData", "ExtensionName": null, "CanCancel": "tNO", "CanDelete": "tYES", "CanLog": "tYES", "ManageSeries": "tNO", "CanFind": "tYES", "CanYearTransfer": "tNO", "Name": "EPY_CTCA", "CanClose": "tNO", "OverwriteDllfile": "tYES", "UseUniqueFormType": "tYES", "CanArchive": "tNO", "MenuItem": "tNO", "MenuCaption": "", "FatherMenuID": null, "Position": null, "MenuUID": "EPY_CTCA", "EnableEnhancedForm": "tYES", "RebuildEnhancedForm": "tNO", "FormSRF": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r<Application>\r  <forms>\r    <action type=\"add\">\r      <form appformnumber=\"-1\" FormType=\"UDO_FT_EPY_CTCA\" type=\"0\" BorderStyle=\"0\" uid=\"UDO_F_EPY_CTCA\" title=\"Cotización de monedas-Vendedor\" visible=\"1\" default_button=\"\" mode=\"3\" pane=\"1\" color=\"0\" left=\"370\" top=\"127\" width=\"600\" height=\"362\" AutoManaged=\"1\" SupportedModes=\"15\" ObjectType=\"EPY_CTCA\">\r        <datasources>\r          <dbdatasources>\r            <action type=\"add\">\r              <datasource tablename=\"@EPY_CTCA\" />\r              <datasource tablename=\"@EPY_CTDE\" />\r            </action>\r          </dbdatasources>\r          <userdatasources>\r            <action type=\"add\">\r              <datasource uid=\"FolderDS\" type=\"9\" size=\"10\" />\r            </action>\r          </userdatasources>\r        </datasources>\r        <Menus />\r        <items>\r          <action type=\"add\">\r            <item uid=\"0_U_S\" type=\"8\" left=\"6\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"0_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Code\" />\r            </item>\r            <item uid=\"0_U_E\" type=\"16\" left=\"127\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Code\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"4\">\r                <databind databound=\"1\" table=\"@EPY_CTCA\" alias=\"Code\" />\r              </specific>\r            </item>\r            <item uid=\"13_U_S\" type=\"8\" left=\"306\" tab_order=\"0\" width=\"121\" top=\"6\" height=\"14\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Fecha\" linkto=\"13_U_E\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Fecha\" />\r            </item>\r            <item uid=\"13_U_E\" type=\"16\" left=\"427\" tab_order=\"0\" width=\"148\" top=\"6\" height=\"14\" visible=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" description=\"Fecha\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific TabOrder=\"5\">\r                <databind databound=\"1\" table=\"@EPY_CTCA\" alias=\"U_EPY_FECH\" />\r              </specific>\r            </item>\r            <item uid=\"1\" type=\"4\" left=\"6\" tab_order=\"\" width=\"65\" top=\"302\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"OK\" />\r            </item>\r            <item uid=\"2\" type=\"4\" left=\"76\" tab_order=\"\" width=\"65\" top=\"302\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Cancelar\" />\r            </item>\r            <item uid=\"0_U_FD\" type=\"99\" left=\"6\" tab_order=\"\" width=\"116\" top=\"58\" height=\"19\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific caption=\"Cotización de moneda Detalle\" AffectsFormMode=\"1\" val_on=\"Y\" val_off=\"N\" pane=\"1\" AutoPaneSelection=\"1\">\r                <databind databound=\"1\" table=\"\" alias=\"FolderDS\" />\r              </specific>\r            </item>\r            <item uid=\"U_RC\" type=\"100\" left=\"5\" tab_order=\"\" width=\"570\" top=\"76\" height=\"207\" visible=\"1\" enabled=\"1\" from_pane=\"0\" to_pane=\"0\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r            </item>\r            <item uid=\"0_U_G\" type=\"127\" left=\"15\" tab_order=\"\" width=\"555\" top=\"103\" height=\"163\" visible=\"1\" enabled=\"1\" from_pane=\"1\" to_pane=\"1\" disp_desc=\"\" right_just=\"0\" linkto=\"\" forecolor=\"-1\" backcolor=\"-1\" text_style=\"0\" font_size=\"-1\" supp_zeros=\"0\" AffectsFormMode=\"1\" IsAutoGenerated=\"1\">\r              <AutoManagedAttribute />\r              <specific SelectionMode=\"2\">\r                <columns>\r                  <action type=\"add\">\r                    <column uid=\"#\" type=\"16\" title=\"#\" description=\"#\" visible=\"1\" AffectsFormMode=\"1\" width=\"20\" disp_desc=\"0\" editable=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\" />\r                    <column uid=\"C_0_1\" type=\"16\" title=\"LineId\" description=\"LineId\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" editable=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\">\r                      <databind databound=\"1\" table=\"@EPY_CTDE\" alias=\"LineId\" />\r                    </column>\r                    <column uid=\"C_0_2\" type=\"16\" title=\"Código de Moneda\" description=\"Código de Moneda\" visible=\"1\" AffectsFormMode=\"1\" width=\"100\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\">\r                      <databind databound=\"1\" table=\"@EPY_CTDE\" alias=\"U_EPY_MONE\" />\r                    </column>\r                    <column uid=\"C_0_3\" type=\"16\" title=\"Valor Venta\" description=\"Valor Venta\" visible=\"1\" AffectsFormMode=\"1\" width=\"356\" disp_desc=\"0\" right_just=\"0\" val_on=\"Y\" val_off=\"N\" backcolor=\"-1\" forecolor=\"-1\" text_style=\"0\" font_size=\"-1\" IsAutoGenerated=\"1\">\r                      <databind databound=\"1\" table=\"@EPY_CTDE\" alias=\"U_EPY_VVTA\" />\r                    </column>\r                  </action>\r                </columns>\r              </specific>\r            </item>\r          </action>\r        </items>\r        <ChooseFromListCollection />\r        <DataBrowser BrowseBy=\"0_U_E\" />\r        <Settings Enabled=\"1\" EnableRowFormat=\"1\" />\r        <items>\r          <action type=\"group\">\r            <item uid=\"0_U_FD\" IsAutoGenerated=\"0\" />\r          </action>\r        </items>\r      </form>\r    </action>\r  </forms>\r</Application>", "ApplyAuthorization": "tNO", "UserObjectMD_ChildTables": [{"SonNumber": 1, "TableName": "EPY_CTDE", "LogTableName": "AEPY_CTDE", "Code": "EPY_CTCA", "ObjectName": "EPY_CTDE"}], "UserObjectMD_FindColumns": [{"ColumnNumber": 1, "ColumnAlias": "Code", "ColumnDescription": "Code", "Code": "EPY_CTCA"}, {"ColumnNumber": 2, "ColumnAlias": "Name", "ColumnDescription": "Name", "Code": "EPY_CTCA"}, {"ColumnNumber": 3, "ColumnAlias": "U_EPY_FECH", "ColumnDescription": "<PERSON><PERSON>", "Code": "EPY_CTCA"}], "UserObjectMD_FormColumns": [{"FormColumnAlias": "Code", "FormColumnDescription": "Code", "FormColumnNumber": 1, "SonNumber": 0, "Code": "EPY_CTCA", "Editable": "tYES"}, {"FormColumnAlias": "U_EPY_FECH", "FormColumnDescription": "<PERSON><PERSON>", "FormColumnNumber": 2, "SonNumber": 0, "Code": "EPY_CTCA", "Editable": "tYES"}], "UserObjectMD_EnhancedFormColumns": [{"Code": "EPY_CTCA", "ColumnNumber": 1, "ChildNumber": 1, "ColumnAlias": "LineId", "ColumnDescription": "LineId", "ColumnIsUsed": "tYES", "Editable": "tNO"}, {"Code": "EPY_CTCA", "ColumnNumber": 2, "ChildNumber": 1, "ColumnAlias": "U_EPY_MONE", "ColumnDescription": "<PERSON><PERSON><PERSON>", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CTCA", "ColumnNumber": 3, "ChildNumber": 1, "ColumnAlias": "U_EPY_VVTA", "ColumnDescription": "<PERSON><PERSON>", "ColumnIsUsed": "tYES", "Editable": "tYES"}, {"Code": "EPY_CTCA", "ColumnNumber": 4, "ChildNumber": 1, "ColumnAlias": "U_EPY_VCOB", "ColumnDescription": "Valor mesa de cambio", "ColumnIsUsed": "tYES", "Editable": "tYES"}]}], "odata.nextLink": "UserObjectsMD?$skip=20"}}