{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#BusinessPartners", "value": [{"@odata.etag": "W/\"1B6453892473A467D07372D45EB05ABC2031647A\"", "CardCode": "CLIE000645", "CardName": "CARLOS VILLAMAYOR", "CardType": "cCustomer", "GroupCode": 100, "Address": "-", "ZipCode": null, "MailAddress": null, "MailZipCode": null, "Phone1": null, "Phone2": null, "Fax": null, "ContactPerson": null, "Notes": null, "PayTermsGrpCode": -1, "CreditLimit": 0, "MaxCommitment": 0, "DiscountPercent": 0, "VatLiable": "vLiable", "FederalTaxID": "2215361-6", "DeductibleAtSource": "tNO", "DeductionPercent": 0, "DeductionValidUntil": null, "PriceListNum": 1, "IntrestRatePercent": 0, "CommissionPercent": 0, "CommissionGroupCode": 0, "FreeText": null, "SalesPersonCode": -1, "Currency": "GS", "RateDiffAccount": null, "Cellular": null, "AvarageLate": null, "City": null, "County": null, "Country": "PY", "MailCity": null, "MailCounty": null, "MailCountry": null, "EmailAddress": null, "Picture": null, "DefaultAccount": null, "DefaultBranch": null, "DefaultBankCode": "-1", "AdditionalID": null, "Pager": null, "FatherCard": null, "CardForeignName": "CARLOS VILLAMAYOR", "FatherType": "cPayments_sum", "DeductionOffice": null, "ExportCode": null, "MinIntrest": 0, "CurrentAccountBalance": 0, "OpenDeliveryNotesBalance": 0, "OpenOrdersBalance": 0, "OpenChecksBalance": 0, "VatGroup": null, "ShippingType": null, "Password": null, "Indicator": null, "IBAN": null, "CreditCardCode": -1, "CreditCardNum": null, "CreditCardExpiration": null, "DebitorAccount": "********", "OpenOpportunities": null, "Valid": "tYES", "ValidFrom": null, "ValidTo": null, "ValidRemarks": null, "Frozen": "tNO", "FrozenFrom": null, "FrozenTo": null, "FrozenRemarks": null, "Block": null, "BillToState": null, "ShipToState": null, "ExemptNum": null, "Priority": -1, "FormCode1099": null, "Box1099": null, "PeymentMethodCode": null, "BackOrder": "tYES", "PartialDelivery": "tYES", "BlockDunning": "tNO", "BankCountry": "PY", "HouseBank": null, "HouseBankCountry": "PY", "HouseBankAccount": null, "ShipToDefault": null, "DunningLevel": null, "DunningDate": null, "CollectionAuthorization": "tNO", "DME": null, "InstructionKey": null, "SinglePayment": "tNO", "ISRBillerID": null, "PaymentBlock": "tNO", "ReferenceDetails": null, "HouseBankBranch": null, "OwnerIDNumber": null, "PaymentBlockDescription": -1, "TaxExemptionLetterNum": null, "MaxAmountOfExemption": 0, "ExemptionValidityDateFrom": null, "ExemptionValidityDateTo": null, "LinkedBusinessPartner": null, "LastMultiReconciliationNum": null, "DeferredTax": "tNO", "Equalization": "tNO", "SubjectToWithholdingTax": "boNO", "CertificateNumber": null, "ExpirationDate": null, "NationalInsuranceNum": null, "AccrualCriteria": "tNO", "WTCode": null, "BillToBuildingFloorRoom": null, "DownPaymentClearAct": "", "ChannelBP": null, "DefaultTechnician": null, "BilltoDefault": "de Facturacion", "CustomerBillofExchangDisc": null, "Territory": null, "ShipToBuildingFloorRoom": null, "CustomerBillofExchangPres": null, "ProjectCode": null, "VatGroupLatinAmerica": null, "DunningTerm": null, "Website": null, "OtherReceivablePayable": null, "BillofExchangeonCollection": null, "CompanyPrivate": "cCompany", "LanguageCode": 25, "UnpaidBillofExchange": null, "WithholdingTaxDeductionGroup": -1, "ClosingDateProcedureNumber": null, "Profession": null, "BankChargesAllocationCode": null, "TaxRoundingRule": "trr_CompanyDefault", "Properties1": "tNO", "Properties2": "tNO", "Properties3": "tNO", "Properties4": "tNO", "Properties5": "tNO", "Properties6": "tNO", "Properties7": "tNO", "Properties8": "tNO", "Properties9": "tNO", "Properties10": "tNO", "Properties11": "tNO", "Properties12": "tNO", "Properties13": "tNO", "Properties14": "tNO", "Properties15": "tNO", "Properties16": "tNO", "Properties17": "tNO", "Properties18": "tNO", "Properties19": "tNO", "Properties20": "tNO", "Properties21": "tNO", "Properties22": "tNO", "Properties23": "tNO", "Properties24": "tNO", "Properties25": "tNO", "Properties26": "tNO", "Properties27": "tNO", "Properties28": "tNO", "Properties29": "tNO", "Properties30": "tNO", "Properties31": "tNO", "Properties32": "tNO", "Properties33": "tNO", "Properties34": "tNO", "Properties35": "tNO", "Properties36": "tNO", "Properties37": "tNO", "Properties38": "tNO", "Properties39": "tNO", "Properties40": "tNO", "Properties41": "tNO", "Properties42": "tNO", "Properties43": "tNO", "Properties44": "tNO", "Properties45": "tNO", "Properties46": "tNO", "Properties47": "tNO", "Properties48": "tNO", "Properties49": "tNO", "Properties50": "tNO", "Properties51": "tNO", "Properties52": "tNO", "Properties53": "tNO", "Properties54": "tNO", "Properties55": "tNO", "Properties56": "tNO", "Properties57": "tNO", "Properties58": "tNO", "Properties59": "tNO", "Properties60": "tNO", "Properties61": "tNO", "Properties62": "tNO", "Properties63": "tNO", "Properties64": "tNO", "CompanyRegistrationNumber": null, "VerificationNumber": null, "DiscountBaseObject": "dgboNone", "DiscountRelations": "dgrLowestDiscount", "TypeReport": "atCompany", "ThresholdOverlook": "tNO", "SurchargeOverlook": "tNO", "Remark1": null, "ConCerti": null, "DownPaymentInterimAccount": null, "OperationCode347": "ocSalesOrServicesRevenues", "InsuranceOperation347": "tNO", "HierarchicalDeduction": "tNO", "ShaamGroup": "sgServicesAndAsset", "WithholdingTaxCertified": "tNO", "BookkeepingCertified": "tNO", "PlanningGroup": null, "Affiliate": "tNO", "Industry": null, "VatIDNum": null, "DatevAccount": null, "DatevFirstDataEntry": "tYES", "UseShippedGoodsAccount": "tNO", "GTSRegNo": null, "GTSBankAccountNo": null, "GTSBillingAddrTel": null, "ETaxWebSite": null, "HouseBankIBAN": "", "VATRegistrationNumber": null, "RepresentativeName": null, "IndustryType": null, "BusinessType": null, "Series": 942, "AutomaticPosting": "apNo", "InterestAccount": null, "FeeAccount": null, "CampaignNumber": null, "AliasName": null, "DefaultBlanketAgreementNumber": null, "EffectiveDiscount": "dgrLowestDiscount", "NoDiscounts": "tNO", "EffectivePrice": "epDefaultPriority", "EffectivePriceConsidersPriceBeforeDiscount": "tNO", "GlobalLocationNumber": null, "EDISenderID": null, "EDIRecipientID": null, "ResidenNumber": "rntSpanishFiscalID", "RelationshipCode": null, "RelationshipDateFrom": null, "RelationshipDateTill": null, "UnifiedFederalTaxID": null, "AttachmentEntry": null, "TypeOfOperation": null, "EndorsableChecksFromBP": "tYES", "AcceptsEndorsedChecks": "tNO", "OwnerCode": null, "BlockSendingMarketingContent": "tNO", "AgentCode": null, "PriceMode": null, "EDocGenerationType": null, "EDocStreet": null, "EDocStreetNumber": null, "EDocBuildingNumber": null, "EDocZipCode": null, "EDocCity": null, "EDocCountry": null, "EDocDistrict": null, "EDocRepresentativeFirstName": null, "EDocRepresentativeSurname": null, "EDocRepresentativeCompany": null, "EDocRepresentativeFiscalCode": null, "EDocRepresentativeAdditionalId": null, "EDocPECAddress": null, "IPACodeForPA": null, "UpdateDate": "2025-01-01T00:00:00Z", "UpdateTime": "21:20:50", "ExemptionMaxAmountValidationType": "emaIndividual", "ECommerceMerchantID": null, "UseBillToAddrToDetermineTax": "tNO", "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "18:32:43", "DefaultTransporterEntry": null, "DefaultTransporterLineNumber": null, "FCERelevant": "tNO", "FCEValidateBaseDelivery": "tNO", "MainUsage": null, "EBooksVATExemptionCause": null, "LegalText": null, "DataVersion": 4, "ExchangeRateForIncomingPayment": "tYES", "ExchangeRateForOutgoingPayment": "tYES", "CertificateDetails": null, "DefaultCurrency": null, "EORINumber": null, "FCEAsPaymentMeans": "tNO", "NotRelevantForMonthlyInvoice": "tNO", "U_BENF": null, "U_CBRT": "S", "U_CO_RENT": null, "U_CRID": "IDENTIFICACION_TRIBUTARIA", "U_CRSI": "contribuyente", "U_MDP": "6", "U_NIDEN": null, "U_PCFV": null, "U_PRI": null, "U_REPL": null, "U_RRIV": "S", "U_TIPCONT": 1, "U_EPY_TSNF": null, "U_NCCO": null, "U_NCON": null, "U_OTRO": null, "U_TCON": "1", "U_EXX_FE_TipoOperacion": "2", "U_TipoTrato": "N", "U_Especialidad": null, "U_Cargo": null, "U_Proyecto": null, "U_AntTope": 0, "U_RUT": null, "U_PrcRet": null, "U_PrcAnt": null, "ElectronicProtocols": [], "BPAddresses": [{"AddressName": "de Facturacion", "Street": "-", "Block": null, "ZipCode": null, "City": null, "County": null, "Country": "PY", "State": null, "FederalTaxID": null, "TaxCode": null, "BuildingFloorRoom": null, "AddressType": "bo_BillTo", "AddressName2": null, "AddressName3": null, "TypeOfAddress": null, "StreetNo": "0", "BPCode": "CLIE000645", "RowNum": 0, "GlobalLocationNumber": null, "Nationality": null, "TaxOffice": null, "GSTIN": null, "GstType": null, "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "21:20:50", "MYFType": null, "TaasEnabled": "tYES", "U_EXX_FE_DEPT": "1", "U_EXX_FE_DIST": "1", "U_EXX_FE_BALO": "1", "U_EXX_FE_BARR": null}], "ContactEmployees": [], "BPAccountReceivablePaybleCollection": [], "BPPaymentMethods": [], "BPWithholdingTaxCollection": [], "BPPaymentDates": [], "BPBranchAssignment": [{"BPCode": "CLIE000645", "BPLID": 1, "DisabledForBP": "tNO"}], "BPBankAccounts": [], "BPFiscalTaxIDCollection": [], "DiscountGroups": [], "BPIntrastatExtension": [], "BPBlockSendingMarketingContents": [], "BPCurrenciesCollection": []}, {"@odata.etag": "W/\"1B6453892473A467D07372D45EB05ABC2031647A\"", "CardCode": "CLIE000646", "CardName": "JUAN CRICHINO", "CardType": "cCustomer", "GroupCode": 100, "Address": "-", "ZipCode": null, "MailAddress": null, "MailZipCode": null, "Phone1": null, "Phone2": null, "Fax": null, "ContactPerson": null, "Notes": null, "PayTermsGrpCode": -1, "CreditLimit": 0, "MaxCommitment": 0, "DiscountPercent": 0, "VatLiable": "vLiable", "FederalTaxID": "1260243-4", "DeductibleAtSource": "tNO", "DeductionPercent": 0, "DeductionValidUntil": null, "PriceListNum": 1, "IntrestRatePercent": 0, "CommissionPercent": 0, "CommissionGroupCode": 0, "FreeText": null, "SalesPersonCode": -1, "Currency": "GS", "RateDiffAccount": null, "Cellular": null, "AvarageLate": null, "City": null, "County": null, "Country": "PY", "MailCity": null, "MailCounty": null, "MailCountry": null, "EmailAddress": null, "Picture": null, "DefaultAccount": null, "DefaultBranch": null, "DefaultBankCode": "-1", "AdditionalID": null, "Pager": null, "FatherCard": null, "CardForeignName": "JUAN CRICHINO", "FatherType": "cPayments_sum", "DeductionOffice": null, "ExportCode": null, "MinIntrest": 0, "CurrentAccountBalance": 0, "OpenDeliveryNotesBalance": 0, "OpenOrdersBalance": 0, "OpenChecksBalance": 0, "VatGroup": null, "ShippingType": null, "Password": null, "Indicator": null, "IBAN": null, "CreditCardCode": -1, "CreditCardNum": null, "CreditCardExpiration": null, "DebitorAccount": "********", "OpenOpportunities": null, "Valid": "tYES", "ValidFrom": null, "ValidTo": null, "ValidRemarks": null, "Frozen": "tNO", "FrozenFrom": null, "FrozenTo": null, "FrozenRemarks": null, "Block": null, "BillToState": null, "ShipToState": null, "ExemptNum": null, "Priority": -1, "FormCode1099": null, "Box1099": null, "PeymentMethodCode": null, "BackOrder": "tYES", "PartialDelivery": "tYES", "BlockDunning": "tNO", "BankCountry": "PY", "HouseBank": null, "HouseBankCountry": "PY", "HouseBankAccount": null, "ShipToDefault": null, "DunningLevel": null, "DunningDate": null, "CollectionAuthorization": "tNO", "DME": null, "InstructionKey": null, "SinglePayment": "tNO", "ISRBillerID": null, "PaymentBlock": "tNO", "ReferenceDetails": null, "HouseBankBranch": null, "OwnerIDNumber": null, "PaymentBlockDescription": -1, "TaxExemptionLetterNum": null, "MaxAmountOfExemption": 0, "ExemptionValidityDateFrom": null, "ExemptionValidityDateTo": null, "LinkedBusinessPartner": null, "LastMultiReconciliationNum": null, "DeferredTax": "tNO", "Equalization": "tNO", "SubjectToWithholdingTax": "boNO", "CertificateNumber": null, "ExpirationDate": null, "NationalInsuranceNum": null, "AccrualCriteria": "tNO", "WTCode": null, "BillToBuildingFloorRoom": null, "DownPaymentClearAct": "", "ChannelBP": null, "DefaultTechnician": null, "BilltoDefault": "de Facturacion", "CustomerBillofExchangDisc": null, "Territory": null, "ShipToBuildingFloorRoom": null, "CustomerBillofExchangPres": null, "ProjectCode": null, "VatGroupLatinAmerica": null, "DunningTerm": null, "Website": null, "OtherReceivablePayable": null, "BillofExchangeonCollection": null, "CompanyPrivate": "cCompany", "LanguageCode": 25, "UnpaidBillofExchange": null, "WithholdingTaxDeductionGroup": -1, "ClosingDateProcedureNumber": null, "Profession": null, "BankChargesAllocationCode": null, "TaxRoundingRule": "trr_CompanyDefault", "Properties1": "tNO", "Properties2": "tNO", "Properties3": "tNO", "Properties4": "tNO", "Properties5": "tNO", "Properties6": "tNO", "Properties7": "tNO", "Properties8": "tNO", "Properties9": "tNO", "Properties10": "tNO", "Properties11": "tNO", "Properties12": "tNO", "Properties13": "tNO", "Properties14": "tNO", "Properties15": "tNO", "Properties16": "tNO", "Properties17": "tNO", "Properties18": "tNO", "Properties19": "tNO", "Properties20": "tNO", "Properties21": "tNO", "Properties22": "tNO", "Properties23": "tNO", "Properties24": "tNO", "Properties25": "tNO", "Properties26": "tNO", "Properties27": "tNO", "Properties28": "tNO", "Properties29": "tNO", "Properties30": "tNO", "Properties31": "tNO", "Properties32": "tNO", "Properties33": "tNO", "Properties34": "tNO", "Properties35": "tNO", "Properties36": "tNO", "Properties37": "tNO", "Properties38": "tNO", "Properties39": "tNO", "Properties40": "tNO", "Properties41": "tNO", "Properties42": "tNO", "Properties43": "tNO", "Properties44": "tNO", "Properties45": "tNO", "Properties46": "tNO", "Properties47": "tNO", "Properties48": "tNO", "Properties49": "tNO", "Properties50": "tNO", "Properties51": "tNO", "Properties52": "tNO", "Properties53": "tNO", "Properties54": "tNO", "Properties55": "tNO", "Properties56": "tNO", "Properties57": "tNO", "Properties58": "tNO", "Properties59": "tNO", "Properties60": "tNO", "Properties61": "tNO", "Properties62": "tNO", "Properties63": "tNO", "Properties64": "tNO", "CompanyRegistrationNumber": null, "VerificationNumber": null, "DiscountBaseObject": "dgboNone", "DiscountRelations": "dgrLowestDiscount", "TypeReport": "atCompany", "ThresholdOverlook": "tNO", "SurchargeOverlook": "tNO", "Remark1": null, "ConCerti": null, "DownPaymentInterimAccount": null, "OperationCode347": "ocSalesOrServicesRevenues", "InsuranceOperation347": "tNO", "HierarchicalDeduction": "tNO", "ShaamGroup": "sgServicesAndAsset", "WithholdingTaxCertified": "tNO", "BookkeepingCertified": "tNO", "PlanningGroup": null, "Affiliate": "tNO", "Industry": null, "VatIDNum": null, "DatevAccount": null, "DatevFirstDataEntry": "tYES", "UseShippedGoodsAccount": "tNO", "GTSRegNo": null, "GTSBankAccountNo": null, "GTSBillingAddrTel": null, "ETaxWebSite": null, "HouseBankIBAN": "", "VATRegistrationNumber": null, "RepresentativeName": null, "IndustryType": null, "BusinessType": null, "Series": 942, "AutomaticPosting": "apNo", "InterestAccount": null, "FeeAccount": null, "CampaignNumber": null, "AliasName": null, "DefaultBlanketAgreementNumber": null, "EffectiveDiscount": "dgrLowestDiscount", "NoDiscounts": "tNO", "EffectivePrice": "epDefaultPriority", "EffectivePriceConsidersPriceBeforeDiscount": "tNO", "GlobalLocationNumber": null, "EDISenderID": null, "EDIRecipientID": null, "ResidenNumber": "rntSpanishFiscalID", "RelationshipCode": null, "RelationshipDateFrom": null, "RelationshipDateTill": null, "UnifiedFederalTaxID": null, "AttachmentEntry": null, "TypeOfOperation": null, "EndorsableChecksFromBP": "tYES", "AcceptsEndorsedChecks": "tNO", "OwnerCode": null, "BlockSendingMarketingContent": "tNO", "AgentCode": null, "PriceMode": null, "EDocGenerationType": null, "EDocStreet": null, "EDocStreetNumber": null, "EDocBuildingNumber": null, "EDocZipCode": null, "EDocCity": null, "EDocCountry": null, "EDocDistrict": null, "EDocRepresentativeFirstName": null, "EDocRepresentativeSurname": null, "EDocRepresentativeCompany": null, "EDocRepresentativeFiscalCode": null, "EDocRepresentativeAdditionalId": null, "EDocPECAddress": null, "IPACodeForPA": null, "UpdateDate": "2025-01-01T00:00:00Z", "UpdateTime": "21:20:50", "ExemptionMaxAmountValidationType": "emaIndividual", "ECommerceMerchantID": null, "UseBillToAddrToDetermineTax": "tNO", "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "18:32:43", "DefaultTransporterEntry": null, "DefaultTransporterLineNumber": null, "FCERelevant": "tNO", "FCEValidateBaseDelivery": "tNO", "MainUsage": null, "EBooksVATExemptionCause": null, "LegalText": null, "DataVersion": 4, "ExchangeRateForIncomingPayment": "tYES", "ExchangeRateForOutgoingPayment": "tYES", "CertificateDetails": null, "DefaultCurrency": null, "EORINumber": null, "FCEAsPaymentMeans": "tNO", "NotRelevantForMonthlyInvoice": "tNO", "U_BENF": null, "U_CBRT": "S", "U_CO_RENT": null, "U_CRID": "IDENTIFICACION_TRIBUTARIA", "U_CRSI": "contribuyente", "U_MDP": "6", "U_NIDEN": null, "U_PCFV": null, "U_PRI": null, "U_REPL": null, "U_RRIV": "S", "U_TIPCONT": 1, "U_EPY_TSNF": null, "U_NCCO": null, "U_NCON": null, "U_OTRO": null, "U_TCON": "1", "U_EXX_FE_TipoOperacion": "2", "U_TipoTrato": "N", "U_Especialidad": null, "U_Cargo": null, "U_Proyecto": null, "U_AntTope": 0, "U_RUT": null, "U_PrcRet": null, "U_PrcAnt": null, "ElectronicProtocols": [], "BPAddresses": [{"AddressName": "de Facturacion", "Street": "-", "Block": null, "ZipCode": null, "City": null, "County": null, "Country": "PY", "State": null, "FederalTaxID": null, "TaxCode": null, "BuildingFloorRoom": null, "AddressType": "bo_BillTo", "AddressName2": null, "AddressName3": null, "TypeOfAddress": null, "StreetNo": "0", "BPCode": "CLIE000646", "RowNum": 0, "GlobalLocationNumber": null, "Nationality": null, "TaxOffice": null, "GSTIN": null, "GstType": null, "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "21:20:50", "MYFType": null, "TaasEnabled": "tYES", "U_EXX_FE_DEPT": "1", "U_EXX_FE_DIST": "1", "U_EXX_FE_BALO": "1", "U_EXX_FE_BARR": null}], "ContactEmployees": [], "BPAccountReceivablePaybleCollection": [], "BPPaymentMethods": [], "BPWithholdingTaxCollection": [], "BPPaymentDates": [], "BPBranchAssignment": [{"BPCode": "CLIE000646", "BPLID": 1, "DisabledForBP": "tNO"}], "BPBankAccounts": [], "BPFiscalTaxIDCollection": [], "DiscountGroups": [], "BPIntrastatExtension": [], "BPBlockSendingMarketingContents": [], "BPCurrenciesCollection": []}, {"@odata.etag": "W/\"1B6453892473A467D07372D45EB05ABC2031647A\"", "CardCode": "CLIE000647", "CardName": "DEP. ARACELI", "CardType": "cCustomer", "GroupCode": 100, "Address": "-", "ZipCode": null, "MailAddress": null, "MailZipCode": null, "Phone1": null, "Phone2": null, "Fax": null, "ContactPerson": null, "Notes": null, "PayTermsGrpCode": -1, "CreditLimit": 0, "MaxCommitment": 0, "DiscountPercent": 0, "VatLiable": "vLiable", "FederalTaxID": "2092380-5", "DeductibleAtSource": "tNO", "DeductionPercent": 0, "DeductionValidUntil": null, "PriceListNum": 1, "IntrestRatePercent": 0, "CommissionPercent": 0, "CommissionGroupCode": 0, "FreeText": null, "SalesPersonCode": -1, "Currency": "GS", "RateDiffAccount": null, "Cellular": null, "AvarageLate": null, "City": null, "County": null, "Country": "PY", "MailCity": null, "MailCounty": null, "MailCountry": null, "EmailAddress": null, "Picture": null, "DefaultAccount": null, "DefaultBranch": null, "DefaultBankCode": "-1", "AdditionalID": null, "Pager": null, "FatherCard": null, "CardForeignName": "DEP. ARACELI", "FatherType": "cPayments_sum", "DeductionOffice": null, "ExportCode": null, "MinIntrest": 0, "CurrentAccountBalance": 0, "OpenDeliveryNotesBalance": 0, "OpenOrdersBalance": 0, "OpenChecksBalance": 0, "VatGroup": null, "ShippingType": null, "Password": null, "Indicator": null, "IBAN": null, "CreditCardCode": -1, "CreditCardNum": null, "CreditCardExpiration": null, "DebitorAccount": "********", "OpenOpportunities": null, "Valid": "tYES", "ValidFrom": null, "ValidTo": null, "ValidRemarks": null, "Frozen": "tNO", "FrozenFrom": null, "FrozenTo": null, "FrozenRemarks": null, "Block": null, "BillToState": null, "ShipToState": null, "ExemptNum": null, "Priority": -1, "FormCode1099": null, "Box1099": null, "PeymentMethodCode": null, "BackOrder": "tYES", "PartialDelivery": "tYES", "BlockDunning": "tNO", "BankCountry": "PY", "HouseBank": null, "HouseBankCountry": "PY", "HouseBankAccount": null, "ShipToDefault": null, "DunningLevel": null, "DunningDate": null, "CollectionAuthorization": "tNO", "DME": null, "InstructionKey": null, "SinglePayment": "tNO", "ISRBillerID": null, "PaymentBlock": "tNO", "ReferenceDetails": null, "HouseBankBranch": null, "OwnerIDNumber": null, "PaymentBlockDescription": -1, "TaxExemptionLetterNum": null, "MaxAmountOfExemption": 0, "ExemptionValidityDateFrom": null, "ExemptionValidityDateTo": null, "LinkedBusinessPartner": null, "LastMultiReconciliationNum": null, "DeferredTax": "tNO", "Equalization": "tNO", "SubjectToWithholdingTax": "boNO", "CertificateNumber": null, "ExpirationDate": null, "NationalInsuranceNum": null, "AccrualCriteria": "tNO", "WTCode": null, "BillToBuildingFloorRoom": null, "DownPaymentClearAct": "", "ChannelBP": null, "DefaultTechnician": null, "BilltoDefault": "de Facturacion", "CustomerBillofExchangDisc": null, "Territory": null, "ShipToBuildingFloorRoom": null, "CustomerBillofExchangPres": null, "ProjectCode": null, "VatGroupLatinAmerica": null, "DunningTerm": null, "Website": null, "OtherReceivablePayable": null, "BillofExchangeonCollection": null, "CompanyPrivate": "cCompany", "LanguageCode": 25, "UnpaidBillofExchange": null, "WithholdingTaxDeductionGroup": -1, "ClosingDateProcedureNumber": null, "Profession": null, "BankChargesAllocationCode": null, "TaxRoundingRule": "trr_CompanyDefault", "Properties1": "tNO", "Properties2": "tNO", "Properties3": "tNO", "Properties4": "tNO", "Properties5": "tNO", "Properties6": "tNO", "Properties7": "tNO", "Properties8": "tNO", "Properties9": "tNO", "Properties10": "tNO", "Properties11": "tNO", "Properties12": "tNO", "Properties13": "tNO", "Properties14": "tNO", "Properties15": "tNO", "Properties16": "tNO", "Properties17": "tNO", "Properties18": "tNO", "Properties19": "tNO", "Properties20": "tNO", "Properties21": "tNO", "Properties22": "tNO", "Properties23": "tNO", "Properties24": "tNO", "Properties25": "tNO", "Properties26": "tNO", "Properties27": "tNO", "Properties28": "tNO", "Properties29": "tNO", "Properties30": "tNO", "Properties31": "tNO", "Properties32": "tNO", "Properties33": "tNO", "Properties34": "tNO", "Properties35": "tNO", "Properties36": "tNO", "Properties37": "tNO", "Properties38": "tNO", "Properties39": "tNO", "Properties40": "tNO", "Properties41": "tNO", "Properties42": "tNO", "Properties43": "tNO", "Properties44": "tNO", "Properties45": "tNO", "Properties46": "tNO", "Properties47": "tNO", "Properties48": "tNO", "Properties49": "tNO", "Properties50": "tNO", "Properties51": "tNO", "Properties52": "tNO", "Properties53": "tNO", "Properties54": "tNO", "Properties55": "tNO", "Properties56": "tNO", "Properties57": "tNO", "Properties58": "tNO", "Properties59": "tNO", "Properties60": "tNO", "Properties61": "tNO", "Properties62": "tNO", "Properties63": "tNO", "Properties64": "tNO", "CompanyRegistrationNumber": null, "VerificationNumber": null, "DiscountBaseObject": "dgboNone", "DiscountRelations": "dgrLowestDiscount", "TypeReport": "atCompany", "ThresholdOverlook": "tNO", "SurchargeOverlook": "tNO", "Remark1": null, "ConCerti": null, "DownPaymentInterimAccount": null, "OperationCode347": "ocSalesOrServicesRevenues", "InsuranceOperation347": "tNO", "HierarchicalDeduction": "tNO", "ShaamGroup": "sgServicesAndAsset", "WithholdingTaxCertified": "tNO", "BookkeepingCertified": "tNO", "PlanningGroup": null, "Affiliate": "tNO", "Industry": null, "VatIDNum": null, "DatevAccount": null, "DatevFirstDataEntry": "tYES", "UseShippedGoodsAccount": "tNO", "GTSRegNo": null, "GTSBankAccountNo": null, "GTSBillingAddrTel": null, "ETaxWebSite": null, "HouseBankIBAN": "", "VATRegistrationNumber": null, "RepresentativeName": null, "IndustryType": null, "BusinessType": null, "Series": 942, "AutomaticPosting": "apNo", "InterestAccount": null, "FeeAccount": null, "CampaignNumber": null, "AliasName": null, "DefaultBlanketAgreementNumber": null, "EffectiveDiscount": "dgrLowestDiscount", "NoDiscounts": "tNO", "EffectivePrice": "epDefaultPriority", "EffectivePriceConsidersPriceBeforeDiscount": "tNO", "GlobalLocationNumber": null, "EDISenderID": null, "EDIRecipientID": null, "ResidenNumber": "rntSpanishFiscalID", "RelationshipCode": null, "RelationshipDateFrom": null, "RelationshipDateTill": null, "UnifiedFederalTaxID": null, "AttachmentEntry": null, "TypeOfOperation": null, "EndorsableChecksFromBP": "tYES", "AcceptsEndorsedChecks": "tNO", "OwnerCode": null, "BlockSendingMarketingContent": "tNO", "AgentCode": null, "PriceMode": null, "EDocGenerationType": null, "EDocStreet": null, "EDocStreetNumber": null, "EDocBuildingNumber": null, "EDocZipCode": null, "EDocCity": null, "EDocCountry": null, "EDocDistrict": null, "EDocRepresentativeFirstName": null, "EDocRepresentativeSurname": null, "EDocRepresentativeCompany": null, "EDocRepresentativeFiscalCode": null, "EDocRepresentativeAdditionalId": null, "EDocPECAddress": null, "IPACodeForPA": null, "UpdateDate": "2025-01-01T00:00:00Z", "UpdateTime": "21:20:51", "ExemptionMaxAmountValidationType": "emaIndividual", "ECommerceMerchantID": null, "UseBillToAddrToDetermineTax": "tNO", "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "18:32:43", "DefaultTransporterEntry": null, "DefaultTransporterLineNumber": null, "FCERelevant": "tNO", "FCEValidateBaseDelivery": "tNO", "MainUsage": null, "EBooksVATExemptionCause": null, "LegalText": null, "DataVersion": 4, "ExchangeRateForIncomingPayment": "tYES", "ExchangeRateForOutgoingPayment": "tYES", "CertificateDetails": null, "DefaultCurrency": null, "EORINumber": null, "FCEAsPaymentMeans": "tNO", "NotRelevantForMonthlyInvoice": "tNO", "U_BENF": null, "U_CBRT": "S", "U_CO_RENT": null, "U_CRID": "IDENTIFICACION_TRIBUTARIA", "U_CRSI": "contribuyente", "U_MDP": "6", "U_NIDEN": null, "U_PCFV": null, "U_PRI": null, "U_REPL": null, "U_RRIV": "S", "U_TIPCONT": 1, "U_EPY_TSNF": null, "U_NCCO": null, "U_NCON": null, "U_OTRO": null, "U_TCON": "1", "U_EXX_FE_TipoOperacion": "2", "U_TipoTrato": "N", "U_Especialidad": null, "U_Cargo": null, "U_Proyecto": null, "U_AntTope": 0, "U_RUT": null, "U_PrcRet": null, "U_PrcAnt": null, "ElectronicProtocols": [], "BPAddresses": [{"AddressName": "de Facturacion", "Street": "-", "Block": null, "ZipCode": null, "City": null, "County": null, "Country": "PY", "State": null, "FederalTaxID": null, "TaxCode": null, "BuildingFloorRoom": null, "AddressType": "bo_BillTo", "AddressName2": null, "AddressName3": null, "TypeOfAddress": null, "StreetNo": "0", "BPCode": "CLIE000647", "RowNum": 0, "GlobalLocationNumber": null, "Nationality": null, "TaxOffice": null, "GSTIN": null, "GstType": null, "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "21:20:51", "MYFType": null, "TaasEnabled": "tYES", "U_EXX_FE_DEPT": "1", "U_EXX_FE_DIST": "1", "U_EXX_FE_BALO": "1", "U_EXX_FE_BARR": null}], "ContactEmployees": [], "BPAccountReceivablePaybleCollection": [], "BPPaymentMethods": [], "BPWithholdingTaxCollection": [], "BPPaymentDates": [], "BPBranchAssignment": [{"BPCode": "CLIE000647", "BPLID": 1, "DisabledForBP": "tNO"}], "BPBankAccounts": [], "BPFiscalTaxIDCollection": [], "DiscountGroups": [], "BPIntrastatExtension": [], "BPBlockSendingMarketingContents": [], "BPCurrenciesCollection": []}, {"@odata.etag": "W/\"1B6453892473A467D07372D45EB05ABC2031647A\"", "CardCode": "CLIE000650", "CardName": "DEP. SORIA.", "CardType": "cCustomer", "GroupCode": 100, "Address": "-", "ZipCode": null, "MailAddress": null, "MailZipCode": null, "Phone1": null, "Phone2": null, "Fax": null, "ContactPerson": null, "Notes": null, "PayTermsGrpCode": -1, "CreditLimit": 0, "MaxCommitment": 0, "DiscountPercent": 0, "VatLiable": "vLiable", "FederalTaxID": "1193507-3", "DeductibleAtSource": "tNO", "DeductionPercent": 0, "DeductionValidUntil": null, "PriceListNum": 1, "IntrestRatePercent": 0, "CommissionPercent": 0, "CommissionGroupCode": 0, "FreeText": null, "SalesPersonCode": -1, "Currency": "GS", "RateDiffAccount": null, "Cellular": null, "AvarageLate": null, "City": null, "County": null, "Country": "PY", "MailCity": null, "MailCounty": null, "MailCountry": null, "EmailAddress": null, "Picture": null, "DefaultAccount": null, "DefaultBranch": null, "DefaultBankCode": "-1", "AdditionalID": null, "Pager": null, "FatherCard": null, "CardForeignName": "DEP. SORIA.", "FatherType": "cPayments_sum", "DeductionOffice": null, "ExportCode": null, "MinIntrest": 0, "CurrentAccountBalance": 0, "OpenDeliveryNotesBalance": 0, "OpenOrdersBalance": 0, "OpenChecksBalance": 0, "VatGroup": null, "ShippingType": null, "Password": null, "Indicator": null, "IBAN": null, "CreditCardCode": -1, "CreditCardNum": null, "CreditCardExpiration": null, "DebitorAccount": "********", "OpenOpportunities": null, "Valid": "tYES", "ValidFrom": null, "ValidTo": null, "ValidRemarks": null, "Frozen": "tNO", "FrozenFrom": null, "FrozenTo": null, "FrozenRemarks": null, "Block": null, "BillToState": null, "ShipToState": null, "ExemptNum": null, "Priority": -1, "FormCode1099": null, "Box1099": null, "PeymentMethodCode": null, "BackOrder": "tYES", "PartialDelivery": "tYES", "BlockDunning": "tNO", "BankCountry": "PY", "HouseBank": null, "HouseBankCountry": "PY", "HouseBankAccount": null, "ShipToDefault": null, "DunningLevel": null, "DunningDate": null, "CollectionAuthorization": "tNO", "DME": null, "InstructionKey": null, "SinglePayment": "tNO", "ISRBillerID": null, "PaymentBlock": "tNO", "ReferenceDetails": null, "HouseBankBranch": null, "OwnerIDNumber": null, "PaymentBlockDescription": -1, "TaxExemptionLetterNum": null, "MaxAmountOfExemption": 0, "ExemptionValidityDateFrom": null, "ExemptionValidityDateTo": null, "LinkedBusinessPartner": null, "LastMultiReconciliationNum": null, "DeferredTax": "tNO", "Equalization": "tNO", "SubjectToWithholdingTax": "boNO", "CertificateNumber": null, "ExpirationDate": null, "NationalInsuranceNum": null, "AccrualCriteria": "tNO", "WTCode": null, "BillToBuildingFloorRoom": null, "DownPaymentClearAct": "", "ChannelBP": null, "DefaultTechnician": null, "BilltoDefault": "de Facturacion", "CustomerBillofExchangDisc": null, "Territory": null, "ShipToBuildingFloorRoom": null, "CustomerBillofExchangPres": null, "ProjectCode": null, "VatGroupLatinAmerica": null, "DunningTerm": null, "Website": null, "OtherReceivablePayable": null, "BillofExchangeonCollection": null, "CompanyPrivate": "cCompany", "LanguageCode": 25, "UnpaidBillofExchange": null, "WithholdingTaxDeductionGroup": -1, "ClosingDateProcedureNumber": null, "Profession": null, "BankChargesAllocationCode": null, "TaxRoundingRule": "trr_CompanyDefault", "Properties1": "tNO", "Properties2": "tNO", "Properties3": "tNO", "Properties4": "tNO", "Properties5": "tNO", "Properties6": "tNO", "Properties7": "tNO", "Properties8": "tNO", "Properties9": "tNO", "Properties10": "tNO", "Properties11": "tNO", "Properties12": "tNO", "Properties13": "tNO", "Properties14": "tNO", "Properties15": "tNO", "Properties16": "tNO", "Properties17": "tNO", "Properties18": "tNO", "Properties19": "tNO", "Properties20": "tNO", "Properties21": "tNO", "Properties22": "tNO", "Properties23": "tNO", "Properties24": "tNO", "Properties25": "tNO", "Properties26": "tNO", "Properties27": "tNO", "Properties28": "tNO", "Properties29": "tNO", "Properties30": "tNO", "Properties31": "tNO", "Properties32": "tNO", "Properties33": "tNO", "Properties34": "tNO", "Properties35": "tNO", "Properties36": "tNO", "Properties37": "tNO", "Properties38": "tNO", "Properties39": "tNO", "Properties40": "tNO", "Properties41": "tNO", "Properties42": "tNO", "Properties43": "tNO", "Properties44": "tNO", "Properties45": "tNO", "Properties46": "tNO", "Properties47": "tNO", "Properties48": "tNO", "Properties49": "tNO", "Properties50": "tNO", "Properties51": "tNO", "Properties52": "tNO", "Properties53": "tNO", "Properties54": "tNO", "Properties55": "tNO", "Properties56": "tNO", "Properties57": "tNO", "Properties58": "tNO", "Properties59": "tNO", "Properties60": "tNO", "Properties61": "tNO", "Properties62": "tNO", "Properties63": "tNO", "Properties64": "tNO", "CompanyRegistrationNumber": null, "VerificationNumber": null, "DiscountBaseObject": "dgboNone", "DiscountRelations": "dgrLowestDiscount", "TypeReport": "atCompany", "ThresholdOverlook": "tNO", "SurchargeOverlook": "tNO", "Remark1": null, "ConCerti": null, "DownPaymentInterimAccount": null, "OperationCode347": "ocSalesOrServicesRevenues", "InsuranceOperation347": "tNO", "HierarchicalDeduction": "tNO", "ShaamGroup": "sgServicesAndAsset", "WithholdingTaxCertified": "tNO", "BookkeepingCertified": "tNO", "PlanningGroup": null, "Affiliate": "tNO", "Industry": null, "VatIDNum": null, "DatevAccount": null, "DatevFirstDataEntry": "tYES", "UseShippedGoodsAccount": "tNO", "GTSRegNo": null, "GTSBankAccountNo": null, "GTSBillingAddrTel": null, "ETaxWebSite": null, "HouseBankIBAN": "", "VATRegistrationNumber": null, "RepresentativeName": null, "IndustryType": null, "BusinessType": null, "Series": 942, "AutomaticPosting": "apNo", "InterestAccount": null, "FeeAccount": null, "CampaignNumber": null, "AliasName": null, "DefaultBlanketAgreementNumber": null, "EffectiveDiscount": "dgrLowestDiscount", "NoDiscounts": "tNO", "EffectivePrice": "epDefaultPriority", "EffectivePriceConsidersPriceBeforeDiscount": "tNO", "GlobalLocationNumber": null, "EDISenderID": null, "EDIRecipientID": null, "ResidenNumber": "rntSpanishFiscalID", "RelationshipCode": null, "RelationshipDateFrom": null, "RelationshipDateTill": null, "UnifiedFederalTaxID": null, "AttachmentEntry": null, "TypeOfOperation": null, "EndorsableChecksFromBP": "tYES", "AcceptsEndorsedChecks": "tNO", "OwnerCode": null, "BlockSendingMarketingContent": "tNO", "AgentCode": null, "PriceMode": null, "EDocGenerationType": null, "EDocStreet": null, "EDocStreetNumber": null, "EDocBuildingNumber": null, "EDocZipCode": null, "EDocCity": null, "EDocCountry": null, "EDocDistrict": null, "EDocRepresentativeFirstName": null, "EDocRepresentativeSurname": null, "EDocRepresentativeCompany": null, "EDocRepresentativeFiscalCode": null, "EDocRepresentativeAdditionalId": null, "EDocPECAddress": null, "IPACodeForPA": null, "UpdateDate": "2025-01-01T00:00:00Z", "UpdateTime": "21:20:52", "ExemptionMaxAmountValidationType": "emaIndividual", "ECommerceMerchantID": null, "UseBillToAddrToDetermineTax": "tNO", "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "18:32:43", "DefaultTransporterEntry": null, "DefaultTransporterLineNumber": null, "FCERelevant": "tNO", "FCEValidateBaseDelivery": "tNO", "MainUsage": null, "EBooksVATExemptionCause": null, "LegalText": null, "DataVersion": 4, "ExchangeRateForIncomingPayment": "tYES", "ExchangeRateForOutgoingPayment": "tYES", "CertificateDetails": null, "DefaultCurrency": null, "EORINumber": null, "FCEAsPaymentMeans": "tNO", "NotRelevantForMonthlyInvoice": "tNO", "U_BENF": null, "U_CBRT": "S", "U_CO_RENT": null, "U_CRID": "IDENTIFICACION_TRIBUTARIA", "U_CRSI": "contribuyente", "U_MDP": "6", "U_NIDEN": null, "U_PCFV": null, "U_PRI": null, "U_REPL": null, "U_RRIV": "S", "U_TIPCONT": 1, "U_EPY_TSNF": null, "U_NCCO": null, "U_NCON": null, "U_OTRO": null, "U_TCON": "1", "U_EXX_FE_TipoOperacion": "2", "U_TipoTrato": "N", "U_Especialidad": null, "U_Cargo": null, "U_Proyecto": null, "U_AntTope": 0, "U_RUT": null, "U_PrcRet": null, "U_PrcAnt": null, "ElectronicProtocols": [], "BPAddresses": [{"AddressName": "de Facturacion", "Street": "-", "Block": null, "ZipCode": null, "City": null, "County": null, "Country": "PY", "State": null, "FederalTaxID": null, "TaxCode": null, "BuildingFloorRoom": null, "AddressType": "bo_BillTo", "AddressName2": null, "AddressName3": null, "TypeOfAddress": null, "StreetNo": "0", "BPCode": "CLIE000650", "RowNum": 0, "GlobalLocationNumber": null, "Nationality": null, "TaxOffice": null, "GSTIN": null, "GstType": null, "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "21:20:52", "MYFType": null, "TaasEnabled": "tYES", "U_EXX_FE_DEPT": "1", "U_EXX_FE_DIST": "1", "U_EXX_FE_BALO": "1", "U_EXX_FE_BARR": null}], "ContactEmployees": [], "BPAccountReceivablePaybleCollection": [], "BPPaymentMethods": [], "BPWithholdingTaxCollection": [], "BPPaymentDates": [], "BPBranchAssignment": [{"BPCode": "CLIE000650", "BPLID": 1, "DisabledForBP": "tNO"}], "BPBankAccounts": [], "BPFiscalTaxIDCollection": [], "DiscountGroups": [], "BPIntrastatExtension": [], "BPBlockSendingMarketingContents": [], "BPCurrenciesCollection": []}, {"@odata.etag": "W/\"1B6453892473A467D07372D45EB05ABC2031647A\"", "CardCode": "CLIE000651", "CardName": "RUBEN ALEJANDRO LOPEZ AYALA", "CardType": "cCustomer", "GroupCode": 100, "Address": "-", "ZipCode": null, "MailAddress": null, "MailZipCode": null, "Phone1": null, "Phone2": null, "Fax": null, "ContactPerson": null, "Notes": null, "PayTermsGrpCode": -1, "CreditLimit": 0, "MaxCommitment": 0, "DiscountPercent": 0, "VatLiable": "vLiable", "FederalTaxID": "3978776-1", "DeductibleAtSource": "tNO", "DeductionPercent": 0, "DeductionValidUntil": null, "PriceListNum": 1, "IntrestRatePercent": 0, "CommissionPercent": 0, "CommissionGroupCode": 0, "FreeText": null, "SalesPersonCode": -1, "Currency": "GS", "RateDiffAccount": null, "Cellular": null, "AvarageLate": null, "City": null, "County": null, "Country": "PY", "MailCity": null, "MailCounty": null, "MailCountry": null, "EmailAddress": null, "Picture": null, "DefaultAccount": null, "DefaultBranch": null, "DefaultBankCode": "-1", "AdditionalID": null, "Pager": null, "FatherCard": null, "CardForeignName": "RUBEN ALEJANDRO LOPEZ AYALA", "FatherType": "cPayments_sum", "DeductionOffice": null, "ExportCode": null, "MinIntrest": 0, "CurrentAccountBalance": 0, "OpenDeliveryNotesBalance": 0, "OpenOrdersBalance": 0, "OpenChecksBalance": 0, "VatGroup": null, "ShippingType": null, "Password": null, "Indicator": null, "IBAN": null, "CreditCardCode": -1, "CreditCardNum": null, "CreditCardExpiration": null, "DebitorAccount": "********", "OpenOpportunities": null, "Valid": "tYES", "ValidFrom": null, "ValidTo": null, "ValidRemarks": null, "Frozen": "tNO", "FrozenFrom": null, "FrozenTo": null, "FrozenRemarks": null, "Block": null, "BillToState": null, "ShipToState": null, "ExemptNum": null, "Priority": -1, "FormCode1099": null, "Box1099": null, "PeymentMethodCode": null, "BackOrder": "tYES", "PartialDelivery": "tYES", "BlockDunning": "tNO", "BankCountry": "PY", "HouseBank": null, "HouseBankCountry": "PY", "HouseBankAccount": null, "ShipToDefault": null, "DunningLevel": null, "DunningDate": null, "CollectionAuthorization": "tNO", "DME": null, "InstructionKey": null, "SinglePayment": "tNO", "ISRBillerID": null, "PaymentBlock": "tNO", "ReferenceDetails": null, "HouseBankBranch": null, "OwnerIDNumber": null, "PaymentBlockDescription": -1, "TaxExemptionLetterNum": null, "MaxAmountOfExemption": 0, "ExemptionValidityDateFrom": null, "ExemptionValidityDateTo": null, "LinkedBusinessPartner": null, "LastMultiReconciliationNum": null, "DeferredTax": "tNO", "Equalization": "tNO", "SubjectToWithholdingTax": "boNO", "CertificateNumber": null, "ExpirationDate": null, "NationalInsuranceNum": null, "AccrualCriteria": "tNO", "WTCode": null, "BillToBuildingFloorRoom": null, "DownPaymentClearAct": "", "ChannelBP": null, "DefaultTechnician": null, "BilltoDefault": "de Facturacion", "CustomerBillofExchangDisc": null, "Territory": null, "ShipToBuildingFloorRoom": null, "CustomerBillofExchangPres": null, "ProjectCode": null, "VatGroupLatinAmerica": null, "DunningTerm": null, "Website": null, "OtherReceivablePayable": null, "BillofExchangeonCollection": null, "CompanyPrivate": "cCompany", "LanguageCode": 25, "UnpaidBillofExchange": null, "WithholdingTaxDeductionGroup": -1, "ClosingDateProcedureNumber": null, "Profession": null, "BankChargesAllocationCode": null, "TaxRoundingRule": "trr_CompanyDefault", "Properties1": "tNO", "Properties2": "tNO", "Properties3": "tNO", "Properties4": "tNO", "Properties5": "tNO", "Properties6": "tNO", "Properties7": "tNO", "Properties8": "tNO", "Properties9": "tNO", "Properties10": "tNO", "Properties11": "tNO", "Properties12": "tNO", "Properties13": "tNO", "Properties14": "tNO", "Properties15": "tNO", "Properties16": "tNO", "Properties17": "tNO", "Properties18": "tNO", "Properties19": "tNO", "Properties20": "tNO", "Properties21": "tNO", "Properties22": "tNO", "Properties23": "tNO", "Properties24": "tNO", "Properties25": "tNO", "Properties26": "tNO", "Properties27": "tNO", "Properties28": "tNO", "Properties29": "tNO", "Properties30": "tNO", "Properties31": "tNO", "Properties32": "tNO", "Properties33": "tNO", "Properties34": "tNO", "Properties35": "tNO", "Properties36": "tNO", "Properties37": "tNO", "Properties38": "tNO", "Properties39": "tNO", "Properties40": "tNO", "Properties41": "tNO", "Properties42": "tNO", "Properties43": "tNO", "Properties44": "tNO", "Properties45": "tNO", "Properties46": "tNO", "Properties47": "tNO", "Properties48": "tNO", "Properties49": "tNO", "Properties50": "tNO", "Properties51": "tNO", "Properties52": "tNO", "Properties53": "tNO", "Properties54": "tNO", "Properties55": "tNO", "Properties56": "tNO", "Properties57": "tNO", "Properties58": "tNO", "Properties59": "tNO", "Properties60": "tNO", "Properties61": "tNO", "Properties62": "tNO", "Properties63": "tNO", "Properties64": "tNO", "CompanyRegistrationNumber": null, "VerificationNumber": null, "DiscountBaseObject": "dgboNone", "DiscountRelations": "dgrLowestDiscount", "TypeReport": "atCompany", "ThresholdOverlook": "tNO", "SurchargeOverlook": "tNO", "Remark1": null, "ConCerti": null, "DownPaymentInterimAccount": null, "OperationCode347": "ocSalesOrServicesRevenues", "InsuranceOperation347": "tNO", "HierarchicalDeduction": "tNO", "ShaamGroup": "sgServicesAndAsset", "WithholdingTaxCertified": "tNO", "BookkeepingCertified": "tNO", "PlanningGroup": null, "Affiliate": "tNO", "Industry": null, "VatIDNum": null, "DatevAccount": null, "DatevFirstDataEntry": "tYES", "UseShippedGoodsAccount": "tNO", "GTSRegNo": null, "GTSBankAccountNo": null, "GTSBillingAddrTel": null, "ETaxWebSite": null, "HouseBankIBAN": "", "VATRegistrationNumber": null, "RepresentativeName": null, "IndustryType": null, "BusinessType": null, "Series": 942, "AutomaticPosting": "apNo", "InterestAccount": null, "FeeAccount": null, "CampaignNumber": null, "AliasName": null, "DefaultBlanketAgreementNumber": null, "EffectiveDiscount": "dgrLowestDiscount", "NoDiscounts": "tNO", "EffectivePrice": "epDefaultPriority", "EffectivePriceConsidersPriceBeforeDiscount": "tNO", "GlobalLocationNumber": null, "EDISenderID": null, "EDIRecipientID": null, "ResidenNumber": "rntSpanishFiscalID", "RelationshipCode": null, "RelationshipDateFrom": null, "RelationshipDateTill": null, "UnifiedFederalTaxID": null, "AttachmentEntry": null, "TypeOfOperation": null, "EndorsableChecksFromBP": "tYES", "AcceptsEndorsedChecks": "tNO", "OwnerCode": null, "BlockSendingMarketingContent": "tNO", "AgentCode": null, "PriceMode": null, "EDocGenerationType": null, "EDocStreet": null, "EDocStreetNumber": null, "EDocBuildingNumber": null, "EDocZipCode": null, "EDocCity": null, "EDocCountry": null, "EDocDistrict": null, "EDocRepresentativeFirstName": null, "EDocRepresentativeSurname": null, "EDocRepresentativeCompany": null, "EDocRepresentativeFiscalCode": null, "EDocRepresentativeAdditionalId": null, "EDocPECAddress": null, "IPACodeForPA": null, "UpdateDate": "2025-01-01T00:00:00Z", "UpdateTime": "21:20:52", "ExemptionMaxAmountValidationType": "emaIndividual", "ECommerceMerchantID": null, "UseBillToAddrToDetermineTax": "tNO", "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "18:32:43", "DefaultTransporterEntry": null, "DefaultTransporterLineNumber": null, "FCERelevant": "tNO", "FCEValidateBaseDelivery": "tNO", "MainUsage": null, "EBooksVATExemptionCause": null, "LegalText": null, "DataVersion": 4, "ExchangeRateForIncomingPayment": "tYES", "ExchangeRateForOutgoingPayment": "tYES", "CertificateDetails": null, "DefaultCurrency": null, "EORINumber": null, "FCEAsPaymentMeans": "tNO", "NotRelevantForMonthlyInvoice": "tNO", "U_BENF": null, "U_CBRT": "S", "U_CO_RENT": null, "U_CRID": "IDENTIFICACION_TRIBUTARIA", "U_CRSI": "contribuyente", "U_MDP": "6", "U_NIDEN": null, "U_PCFV": null, "U_PRI": null, "U_REPL": null, "U_RRIV": "S", "U_TIPCONT": 1, "U_EPY_TSNF": null, "U_NCCO": null, "U_NCON": null, "U_OTRO": null, "U_TCON": "1", "U_EXX_FE_TipoOperacion": "2", "U_TipoTrato": "N", "U_Especialidad": null, "U_Cargo": null, "U_Proyecto": null, "U_AntTope": 0, "U_RUT": null, "U_PrcRet": null, "U_PrcAnt": null, "ElectronicProtocols": [], "BPAddresses": [{"AddressName": "de Facturacion", "Street": "-", "Block": null, "ZipCode": null, "City": null, "County": null, "Country": "PY", "State": null, "FederalTaxID": null, "TaxCode": null, "BuildingFloorRoom": null, "AddressType": "bo_BillTo", "AddressName2": null, "AddressName3": null, "TypeOfAddress": null, "StreetNo": "0", "BPCode": "CLIE000651", "RowNum": 0, "GlobalLocationNumber": null, "Nationality": null, "TaxOffice": null, "GSTIN": null, "GstType": null, "CreateDate": "2025-01-01T00:00:00Z", "CreateTime": "21:20:52", "MYFType": null, "TaasEnabled": "tYES", "U_EXX_FE_DEPT": "1", "U_EXX_FE_DIST": "1", "U_EXX_FE_BALO": "1", "U_EXX_FE_BARR": null}], "ContactEmployees": [], "BPAccountReceivablePaybleCollection": [], "BPPaymentMethods": [], "BPWithholdingTaxCollection": [], "BPPaymentDates": [], "BPBranchAssignment": [{"BPCode": "CLIE000651", "BPLID": 1, "DisabledForBP": "tNO"}], "BPBankAccounts": [], "BPFiscalTaxIDCollection": [], "DiscountGroups": [], "BPIntrastatExtension": [], "BPBlockSendingMarketingContents": [], "BPCurrenciesCollection": []}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#BusinessPartners\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"@odata.etag\" : \"W/\\\"1B6453892473A467D07372D45EB05ABC2031647A\\\"\",\n\t\t\t\"CardCode\" : \"CLIE000645\",\n\t\t\t\"CardName\" : \"CARLOS VILLAMAYOR\",\n\t\t\t\"CardType\" : \"cCustomer\",\n\t\t\t\"GroupCode\" : 100,\n\t\t\t\"Address\" : \"-\",\n\t\t\t\"ZipCode\" : null,\n\t\t\t\"MailAddress\" : null,\n\t\t\t\"MailZipCode\" : null,\n\t\t\t\"Phone1\" : null,\n\t\t\t\"Phone2\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"ContactPerson\" : null,\n\t\t\t\"Notes\" : null,\n\t\t\t\"PayTermsGrpCode\" : -1,\n\t\t\t\"CreditLimit\" : 0.0,\n\t\t\t\"MaxCommitment\" : 0.0,\n\t\t\t\"DiscountPercent\" : 0.0,\n\t\t\t\"VatLiable\" : \"vLiable\",\n\t\t\t\"FederalTaxID\" : \"2215361-6\",\n\t\t\t\"DeductibleAtSource\" : \"tNO\",\n\t\t\t\"DeductionPercent\" : 0.0,\n\t\t\t\"DeductionValidUntil\" : null,\n\t\t\t\"PriceListNum\" : 1,\n\t\t\t\"IntrestRatePercent\" : 0.0,\n\t\t\t\"CommissionPercent\" : 0.0,\n\t\t\t\"CommissionGroupCode\" : 0,\n\t\t\t\"FreeText\" : null,\n\t\t\t\"SalesPersonCode\" : -1,\n\t\t\t\"Currency\" : \"GS\",\n\t\t\t\"RateDiffAccount\" : null,\n\t\t\t\"Cellular\" : null,\n\t\t\t\"AvarageLate\" : null,\n\t\t\t\"City\" : null,\n\t\t\t\"County\" : null,\n\t\t\t\"Country\" : \"PY\",\n\t\t\t\"MailCity\" : null,\n\t\t\t\"MailCounty\" : null,\n\t\t\t\"MailCountry\" : null,\n\t\t\t\"EmailAddress\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"DefaultAccount\" : null,\n\t\t\t\"DefaultBranch\" : null,\n\t\t\t\"DefaultBankCode\" : \"-1\",\n\t\t\t\"AdditionalID\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"FatherCard\" : null,\n\t\t\t\"CardForeignName\" : \"CARLOS VILLAMAYOR\",\n\t\t\t\"FatherType\" : \"cPayments_sum\",\n\t\t\t\"DeductionOffice\" : null,\n\t\t\t\"ExportCode\" : null,\n\t\t\t\"MinIntrest\" : 0.0,\n\t\t\t\"CurrentAccountBalance\" : 0.0,\n\t\t\t\"OpenDeliveryNotesBalance\" : 0.0,\n\t\t\t\"OpenOrdersBalance\" : 0.0,\n\t\t\t\"OpenChecksBalance\" : 0.0,\n\t\t\t\"VatGroup\" : null,\n\t\t\t\"ShippingType\" : null,\n\t\t\t\"Password\" : null,\n\t\t\t\"Indicator\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CreditCardCode\" : -1,\n\t\t\t\"CreditCardNum\" : null,\n\t\t\t\"CreditCardExpiration\" : null,\n\t\t\t\"DebitorAccount\" : \"********\",\n\t\t\t\"OpenOpportunities\" : null,\n\t\t\t\"Valid\" : \"tYES\",\n\t\t\t\"ValidFrom\" : null,\n\t\t\t\"ValidTo\" : null,\n\t\t\t\"ValidRemarks\" : null,\n\t\t\t\"Frozen\" : \"tNO\",\n\t\t\t\"FrozenFrom\" : null,\n\t\t\t\"FrozenTo\" : null,\n\t\t\t\"FrozenRemarks\" : null,\n\t\t\t\"Block\" : null,\n\t\t\t\"BillToState\" : null,\n\t\t\t\"ShipToState\" : null,\n\t\t\t\"ExemptNum\" : null,\n\t\t\t\"Priority\" : -1,\n\t\t\t\"FormCode1099\" : null,\n\t\t\t\"Box1099\" : null,\n\t\t\t\"PeymentMethodCode\" : null,\n\t\t\t\"BackOrder\" : \"tYES\",\n\t\t\t\"PartialDelivery\" : \"tYES\",\n\t\t\t\"BlockDunning\" : \"tNO\",\n\t\t\t\"BankCountry\" : \"PY\",\n\t\t\t\"HouseBank\" : null,\n\t\t\t\"HouseBankCountry\" : \"PY\",\n\t\t\t\"HouseBankAccount\" : null,\n\t\t\t\"ShipToDefault\" : null,\n\t\t\t\"DunningLevel\" : null,\n\t\t\t\"DunningDate\" : null,\n\t\t\t\"CollectionAuthorization\" : \"tNO\",\n\t\t\t\"DME\" : null,\n\t\t\t\"InstructionKey\" : null,\n\t\t\t\"SinglePayment\" : \"tNO\",\n\t\t\t\"ISRBillerID\" : null,\n\t\t\t\"PaymentBlock\" : \"tNO\",\n\t\t\t\"ReferenceDetails\" : null,\n\t\t\t\"HouseBankBranch\" : null,\n\t\t\t\"OwnerIDNumber\" : null,\n\t\t\t\"PaymentBlockDescription\" : -1,\n\t\t\t\"TaxExemptionLetterNum\" : null,\n\t\t\t\"MaxAmountOfExemption\" : 0.0,\n\t\t\t\"ExemptionValidityDateFrom\" : null,\n\t\t\t\"ExemptionValidityDateTo\" : null,\n\t\t\t\"LinkedBusinessPartner\" : null,\n\t\t\t\"LastMultiReconciliationNum\" : null,\n\t\t\t\"DeferredTax\" : \"tNO\",\n\t\t\t\"Equalization\" : \"tNO\",\n\t\t\t\"SubjectToWithholdingTax\" : \"boNO\",\n\t\t\t\"CertificateNumber\" : null,\n\t\t\t\"ExpirationDate\" : null,\n\t\t\t\"NationalInsuranceNum\" : null,\n\t\t\t\"AccrualCriteria\" : \"tNO\",\n\t\t\t\"WTCode\" : null,\n\t\t\t\"BillToBuildingFloorRoom\" : null,\n\t\t\t\"DownPaymentClearAct\" : \"\",\n\t\t\t\"ChannelBP\" : null,\n\t\t\t\"DefaultTechnician\" : null,\n\t\t\t\"BilltoDefault\" : \"de Facturacion\",\n\t\t\t\"CustomerBillofExchangDisc\" : null,\n\t\t\t\"Territory\" : null,\n\t\t\t\"ShipToBuildingFloorRoom\" : null,\n\t\t\t\"CustomerBillofExchangPres\" : null,\n\t\t\t\"ProjectCode\" : null,\n\t\t\t\"VatGroupLatinAmerica\" : null,\n\t\t\t\"DunningTerm\" : null,\n\t\t\t\"Website\" : null,\n\t\t\t\"OtherReceivablePayable\" : null,\n\t\t\t\"BillofExchangeonCollection\" : null,\n\t\t\t\"CompanyPrivate\" : \"cCompany\",\n\t\t\t\"LanguageCode\" : 25,\n\t\t\t\"UnpaidBillofExchange\" : null,\n\t\t\t\"WithholdingTaxDeductionGroup\" : -1,\n\t\t\t\"ClosingDateProcedureNumber\" : null,\n\t\t\t\"Profession\" : null,\n\t\t\t\"BankChargesAllocationCode\" : null,\n\t\t\t\"TaxRoundingRule\" : \"trr_CompanyDefault\",\n\t\t\t\"Properties1\" : \"tNO\",\n\t\t\t\"Properties2\" : \"tNO\",\n\t\t\t\"Properties3\" : \"tNO\",\n\t\t\t\"Properties4\" : \"tNO\",\n\t\t\t\"Properties5\" : \"tNO\",\n\t\t\t\"Properties6\" : \"tNO\",\n\t\t\t\"Properties7\" : \"tNO\",\n\t\t\t\"Properties8\" : \"tNO\",\n\t\t\t\"Properties9\" : \"tNO\",\n\t\t\t\"Properties10\" : \"tNO\",\n\t\t\t\"Properties11\" : \"tNO\",\n\t\t\t\"Properties12\" : \"tNO\",\n\t\t\t\"Properties13\" : \"tNO\",\n\t\t\t\"Properties14\" : \"tNO\",\n\t\t\t\"Properties15\" : \"tNO\",\n\t\t\t\"Properties16\" : \"tNO\",\n\t\t\t\"Properties17\" : \"tNO\",\n\t\t\t\"Properties18\" : \"tNO\",\n\t\t\t\"Properties19\" : \"tNO\",\n\t\t\t\"Properties20\" : \"tNO\",\n\t\t\t\"Properties21\" : \"tNO\",\n\t\t\t\"Properties22\" : \"tNO\",\n\t\t\t\"Properties23\" : \"tNO\",\n\t\t\t\"Properties24\" : \"tNO\",\n\t\t\t\"Properties25\" : \"tNO\",\n\t\t\t\"Properties26\" : \"tNO\",\n\t\t\t\"Properties27\" : \"tNO\",\n\t\t\t\"Properties28\" : \"tNO\",\n\t\t\t\"Properties29\" : \"tNO\",\n\t\t\t\"Properties30\" : \"tNO\",\n\t\t\t\"Properties31\" : \"tNO\",\n\t\t\t\"Properties32\" : \"tNO\",\n\t\t\t\"Properties33\" : \"tNO\",\n\t\t\t\"Properties34\" : \"tNO\",\n\t\t\t\"Properties35\" : \"tNO\",\n\t\t\t\"Properties36\" : \"tNO\",\n\t\t\t\"Properties37\" : \"tNO\",\n\t\t\t\"Properties38\" : \"tNO\",\n\t\t\t\"Properties39\" : \"tNO\",\n\t\t\t\"Properties40\" : \"tNO\",\n\t\t\t\"Properties41\" : \"tNO\",\n\t\t\t\"Properties42\" : \"tNO\",\n\t\t\t\"Properties43\" : \"tNO\",\n\t\t\t\"Properties44\" : \"tNO\",\n\t\t\t\"Properties45\" : \"tNO\",\n\t\t\t\"Properties46\" : \"tNO\",\n\t\t\t\"Properties47\" : \"tNO\",\n\t\t\t\"Properties48\" : \"tNO\",\n\t\t\t\"Properties49\" : \"tNO\",\n\t\t\t\"Properties50\" : \"tNO\",\n\t\t\t\"Properties51\" : \"tNO\",\n\t\t\t\"Properties52\" : \"tNO\",\n\t\t\t\"Properties53\" : \"tNO\",\n\t\t\t\"Properties54\" : \"tNO\",\n\t\t\t\"Properties55\" : \"tNO\",\n\t\t\t\"Properties56\" : \"tNO\",\n\t\t\t\"Properties57\" : \"tNO\",\n\t\t\t\"Properties58\" : \"tNO\",\n\t\t\t\"Properties59\" : \"tNO\",\n\t\t\t\"Properties60\" : \"tNO\",\n\t\t\t\"Properties61\" : \"tNO\",\n\t\t\t\"Properties62\" : \"tNO\",\n\t\t\t\"Properties63\" : \"tNO\",\n\t\t\t\"Properties64\" : \"tNO\",\n\t\t\t\"CompanyRegistrationNumber\" : null,\n\t\t\t\"VerificationNumber\" : null,\n\t\t\t\"DiscountBaseObject\" : \"dgboNone\",\n\t\t\t\"DiscountRelations\" : \"dgrLowestDiscount\",\n\t\t\t\"TypeReport\" : \"atCompany\",\n\t\t\t\"ThresholdOverlook\" : \"tNO\",\n\t\t\t\"SurchargeOverlook\" : \"tNO\",\n\t\t\t\"Remark1\" : null,\n\t\t\t\"ConCerti\" : null,\n\t\t\t\"DownPaymentInterimAccount\" : null,\n\t\t\t\"OperationCode347\" : \"ocSalesOrServicesRevenues\",\n\t\t\t\"InsuranceOperation347\" : \"tNO\",\n\t\t\t\"HierarchicalDeduction\" : \"tNO\",\n\t\t\t\"ShaamGroup\" : \"sgServicesAndAsset\",\n\t\t\t\"WithholdingTaxCertified\" : \"tNO\",\n\t\t\t\"BookkeepingCertified\" : \"tNO\",\n\t\t\t\"PlanningGroup\" : null,\n\t\t\t\"Affiliate\" : \"tNO\",\n\t\t\t\"Industry\" : null,\n\t\t\t\"VatIDNum\" : null,\n\t\t\t\"DatevAccount\" : null,\n\t\t\t\"DatevFirstDataEntry\" : \"tYES\",\n\t\t\t\"UseShippedGoodsAccount\" : \"tNO\",\n\t\t\t\"GTSRegNo\" : null,\n\t\t\t\"GTSBankAccountNo\" : null,\n\t\t\t\"GTSBillingAddrTel\" : null,\n\t\t\t\"ETaxWebSite\" : null,\n\t\t\t\"HouseBankIBAN\" : \"\",\n\t\t\t\"VATRegistrationNumber\" : null,\n\t\t\t\"RepresentativeName\" : null,\n\t\t\t\"IndustryType\" : null,\n\t\t\t\"BusinessType\" : null,\n\t\t\t\"Series\" : 942,\n\t\t\t\"AutomaticPosting\" : \"apNo\",\n\t\t\t\"InterestAccount\" : null,\n\t\t\t\"FeeAccount\" : null,\n\t\t\t\"CampaignNumber\" : null,\n\t\t\t\"AliasName\" : null,\n\t\t\t\"DefaultBlanketAgreementNumber\" : null,\n\t\t\t\"EffectiveDiscount\" : \"dgrLowestDiscount\",\n\t\t\t\"NoDiscounts\" : \"tNO\",\n\t\t\t\"EffectivePrice\" : \"epDefaultPriority\",\n\t\t\t\"EffectivePriceConsidersPriceBeforeDiscount\" : \"tNO\",\n\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\"EDISenderID\" : null,\n\t\t\t\"EDIRecipientID\" : null,\n\t\t\t\"ResidenNumber\" : \"rntSpanishFiscalID\",\n\t\t\t\"RelationshipCode\" : null,\n\t\t\t\"RelationshipDateFrom\" : null,\n\t\t\t\"RelationshipDateTill\" : null,\n\t\t\t\"UnifiedFederalTaxID\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"TypeOfOperation\" : null,\n\t\t\t\"EndorsableChecksFromBP\" : \"tYES\",\n\t\t\t\"AcceptsEndorsedChecks\" : \"tNO\",\n\t\t\t\"OwnerCode\" : null,\n\t\t\t\"BlockSendingMarketingContent\" : \"tNO\",\n\t\t\t\"AgentCode\" : null,\n\t\t\t\"PriceMode\" : null,\n\t\t\t\"EDocGenerationType\" : null,\n\t\t\t\"EDocStreet\" : null,\n\t\t\t\"EDocStreetNumber\" : null,\n\t\t\t\"EDocBuildingNumber\" : null,\n\t\t\t\"EDocZipCode\" : null,\n\t\t\t\"EDocCity\" : null,\n\t\t\t\"EDocCountry\" : null,\n\t\t\t\"EDocDistrict\" : null,\n\t\t\t\"EDocRepresentativeFirstName\" : null,\n\t\t\t\"EDocRepresentativeSurname\" : null,\n\t\t\t\"EDocRepresentativeCompany\" : null,\n\t\t\t\"EDocRepresentativeFiscalCode\" : null,\n\t\t\t\"EDocRepresentativeAdditionalId\" : null,\n\t\t\t\"EDocPECAddress\" : null,\n\t\t\t\"IPACodeForPA\" : null,\n\t\t\t\"UpdateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"21:20:50\",\n\t\t\t\"ExemptionMaxAmountValidationType\" : \"emaIndividual\",\n\t\t\t\"ECommerceMerchantID\" : null,\n\t\t\t\"UseBillToAddrToDetermineTax\" : \"tNO\",\n\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"18:32:43\",\n\t\t\t\"DefaultTransporterEntry\" : null,\n\t\t\t\"DefaultTransporterLineNumber\" : null,\n\t\t\t\"FCERelevant\" : \"tNO\",\n\t\t\t\"FCEValidateBaseDelivery\" : \"tNO\",\n\t\t\t\"MainUsage\" : null,\n\t\t\t\"EBooksVATExemptionCause\" : null,\n\t\t\t\"LegalText\" : null,\n\t\t\t\"DataVersion\" : 4,\n\t\t\t\"ExchangeRateForIncomingPayment\" : \"tYES\",\n\t\t\t\"ExchangeRateForOutgoingPayment\" : \"tYES\",\n\t\t\t\"CertificateDetails\" : null,\n\t\t\t\"DefaultCurrency\" : null,\n\t\t\t\"EORINumber\" : null,\n\t\t\t\"FCEAsPaymentMeans\" : \"tNO\",\n\t\t\t\"NotRelevantForMonthlyInvoice\" : \"tNO\",\n\t\t\t\"U_BENF\" : null,\n\t\t\t\"U_CBRT\" : \"S\",\n\t\t\t\"U_CO_RENT\" : null,\n\t\t\t\"U_CRID\" : \"IDENTIFICACION_TRIBUTARIA\",\n\t\t\t\"U_CRSI\" : \"contribuyente\",\n\t\t\t\"U_MDP\" : \"6\",\n\t\t\t\"U_NIDEN\" : null,\n\t\t\t\"U_PCFV\" : null,\n\t\t\t\"U_PRI\" : null,\n\t\t\t\"U_REPL\" : null,\n\t\t\t\"U_RRIV\" : \"S\",\n\t\t\t\"U_TIPCONT\" : 1,\n\t\t\t\"U_EPY_TSNF\" : null,\n\t\t\t\"U_NCCO\" : null,\n\t\t\t\"U_NCON\" : null,\n\t\t\t\"U_OTRO\" : null,\n\t\t\t\"U_TCON\" : \"1\",\n\t\t\t\"U_EXX_FE_TipoOperacion\" : \"2\",\n\t\t\t\"U_TipoTrato\" : \"N\",\n\t\t\t\"U_Especialidad\" : null,\n\t\t\t\"U_Cargo\" : null,\n\t\t\t\"U_Proyecto\" : null,\n\t\t\t\"U_AntTope\" : 0.0,\n\t\t\t\"U_RUT\" : null,\n\t\t\t\"U_PrcRet\" : null,\n\t\t\t\"U_PrcAnt\" : null,\n\t\t\t\"ElectronicProtocols\" : [],\n\t\t\t\"BPAddresses\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"AddressName\" : \"de Facturacion\",\n\t\t\t\t\t\"Street\" : \"-\",\n\t\t\t\t\t\"Block\" : null,\n\t\t\t\t\t\"ZipCode\" : null,\n\t\t\t\t\t\"City\" : null,\n\t\t\t\t\t\"County\" : null,\n\t\t\t\t\t\"Country\" : \"PY\",\n\t\t\t\t\t\"State\" : null,\n\t\t\t\t\t\"FederalTaxID\" : null,\n\t\t\t\t\t\"TaxCode\" : null,\n\t\t\t\t\t\"BuildingFloorRoom\" : null,\n\t\t\t\t\t\"AddressType\" : \"bo_BillTo\",\n\t\t\t\t\t\"AddressName2\" : null,\n\t\t\t\t\t\"AddressName3\" : null,\n\t\t\t\t\t\"TypeOfAddress\" : null,\n\t\t\t\t\t\"StreetNo\" : \"0\",\n\t\t\t\t\t\"BPCode\" : \"CLIE000645\",\n\t\t\t\t\t\"RowNum\" : 0,\n\t\t\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\t\t\"Nationality\" : null,\n\t\t\t\t\t\"TaxOffice\" : null,\n\t\t\t\t\t\"GSTIN\" : null,\n\t\t\t\t\t\"GstType\" : null,\n\t\t\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\t\t\"CreateTime\" : \"21:20:50\",\n\t\t\t\t\t\"MYFType\" : null,\n\t\t\t\t\t\"TaasEnabled\" : \"tYES\",\n\t\t\t\t\t\"U_EXX_FE_DEPT\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_DIST\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BALO\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BARR\" : null\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"ContactEmployees\" : [],\n\t\t\t\"BPAccountReceivablePaybleCollection\" : [],\n\t\t\t\"BPPaymentMethods\" : [],\n\t\t\t\"BPWithholdingTaxCollection\" : [],\n\t\t\t\"BPPaymentDates\" : [],\n\t\t\t\"BPBranchAssignment\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"BPCode\" : \"CLIE000645\",\n\t\t\t\t\t\"BPLID\" : 1,\n\t\t\t\t\t\"DisabledForBP\" : \"tNO\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"BPBankAccounts\" : [],\n\t\t\t\"BPFiscalTaxIDCollection\" : [],\n\t\t\t\"DiscountGroups\" : [],\n\t\t\t\"BPIntrastatExtension\" : {},\n\t\t\t\"BPBlockSendingMarketingContents\" : [],\n\t\t\t\"BPCurrenciesCollection\" : []\n\t\t},\n\t\t{\n\t\t\t\"@odata.etag\" : \"W/\\\"1B6453892473A467D07372D45EB05ABC2031647A\\\"\",\n\t\t\t\"CardCode\" : \"CLIE000646\",\n\t\t\t\"CardName\" : \"JUAN CRICHINO\",\n\t\t\t\"CardType\" : \"cCustomer\",\n\t\t\t\"GroupCode\" : 100,\n\t\t\t\"Address\" : \"-\",\n\t\t\t\"ZipCode\" : null,\n\t\t\t\"MailAddress\" : null,\n\t\t\t\"MailZipCode\" : null,\n\t\t\t\"Phone1\" : null,\n\t\t\t\"Phone2\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"ContactPerson\" : null,\n\t\t\t\"Notes\" : null,\n\t\t\t\"PayTermsGrpCode\" : -1,\n\t\t\t\"CreditLimit\" : 0.0,\n\t\t\t\"MaxCommitment\" : 0.0,\n\t\t\t\"DiscountPercent\" : 0.0,\n\t\t\t\"VatLiable\" : \"vLiable\",\n\t\t\t\"FederalTaxID\" : \"1260243-4\",\n\t\t\t\"DeductibleAtSource\" : \"tNO\",\n\t\t\t\"DeductionPercent\" : 0.0,\n\t\t\t\"DeductionValidUntil\" : null,\n\t\t\t\"PriceListNum\" : 1,\n\t\t\t\"IntrestRatePercent\" : 0.0,\n\t\t\t\"CommissionPercent\" : 0.0,\n\t\t\t\"CommissionGroupCode\" : 0,\n\t\t\t\"FreeText\" : null,\n\t\t\t\"SalesPersonCode\" : -1,\n\t\t\t\"Currency\" : \"GS\",\n\t\t\t\"RateDiffAccount\" : null,\n\t\t\t\"Cellular\" : null,\n\t\t\t\"AvarageLate\" : null,\n\t\t\t\"City\" : null,\n\t\t\t\"County\" : null,\n\t\t\t\"Country\" : \"PY\",\n\t\t\t\"MailCity\" : null,\n\t\t\t\"MailCounty\" : null,\n\t\t\t\"MailCountry\" : null,\n\t\t\t\"EmailAddress\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"DefaultAccount\" : null,\n\t\t\t\"DefaultBranch\" : null,\n\t\t\t\"DefaultBankCode\" : \"-1\",\n\t\t\t\"AdditionalID\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"FatherCard\" : null,\n\t\t\t\"CardForeignName\" : \"JUAN CRICHINO\",\n\t\t\t\"FatherType\" : \"cPayments_sum\",\n\t\t\t\"DeductionOffice\" : null,\n\t\t\t\"ExportCode\" : null,\n\t\t\t\"MinIntrest\" : 0.0,\n\t\t\t\"CurrentAccountBalance\" : 0.0,\n\t\t\t\"OpenDeliveryNotesBalance\" : 0.0,\n\t\t\t\"OpenOrdersBalance\" : 0.0,\n\t\t\t\"OpenChecksBalance\" : 0.0,\n\t\t\t\"VatGroup\" : null,\n\t\t\t\"ShippingType\" : null,\n\t\t\t\"Password\" : null,\n\t\t\t\"Indicator\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CreditCardCode\" : -1,\n\t\t\t\"CreditCardNum\" : null,\n\t\t\t\"CreditCardExpiration\" : null,\n\t\t\t\"DebitorAccount\" : \"********\",\n\t\t\t\"OpenOpportunities\" : null,\n\t\t\t\"Valid\" : \"tYES\",\n\t\t\t\"ValidFrom\" : null,\n\t\t\t\"ValidTo\" : null,\n\t\t\t\"ValidRemarks\" : null,\n\t\t\t\"Frozen\" : \"tNO\",\n\t\t\t\"FrozenFrom\" : null,\n\t\t\t\"FrozenTo\" : null,\n\t\t\t\"FrozenRemarks\" : null,\n\t\t\t\"Block\" : null,\n\t\t\t\"BillToState\" : null,\n\t\t\t\"ShipToState\" : null,\n\t\t\t\"ExemptNum\" : null,\n\t\t\t\"Priority\" : -1,\n\t\t\t\"FormCode1099\" : null,\n\t\t\t\"Box1099\" : null,\n\t\t\t\"PeymentMethodCode\" : null,\n\t\t\t\"BackOrder\" : \"tYES\",\n\t\t\t\"PartialDelivery\" : \"tYES\",\n\t\t\t\"BlockDunning\" : \"tNO\",\n\t\t\t\"BankCountry\" : \"PY\",\n\t\t\t\"HouseBank\" : null,\n\t\t\t\"HouseBankCountry\" : \"PY\",\n\t\t\t\"HouseBankAccount\" : null,\n\t\t\t\"ShipToDefault\" : null,\n\t\t\t\"DunningLevel\" : null,\n\t\t\t\"DunningDate\" : null,\n\t\t\t\"CollectionAuthorization\" : \"tNO\",\n\t\t\t\"DME\" : null,\n\t\t\t\"InstructionKey\" : null,\n\t\t\t\"SinglePayment\" : \"tNO\",\n\t\t\t\"ISRBillerID\" : null,\n\t\t\t\"PaymentBlock\" : \"tNO\",\n\t\t\t\"ReferenceDetails\" : null,\n\t\t\t\"HouseBankBranch\" : null,\n\t\t\t\"OwnerIDNumber\" : null,\n\t\t\t\"PaymentBlockDescription\" : -1,\n\t\t\t\"TaxExemptionLetterNum\" : null,\n\t\t\t\"MaxAmountOfExemption\" : 0.0,\n\t\t\t\"ExemptionValidityDateFrom\" : null,\n\t\t\t\"ExemptionValidityDateTo\" : null,\n\t\t\t\"LinkedBusinessPartner\" : null,\n\t\t\t\"LastMultiReconciliationNum\" : null,\n\t\t\t\"DeferredTax\" : \"tNO\",\n\t\t\t\"Equalization\" : \"tNO\",\n\t\t\t\"SubjectToWithholdingTax\" : \"boNO\",\n\t\t\t\"CertificateNumber\" : null,\n\t\t\t\"ExpirationDate\" : null,\n\t\t\t\"NationalInsuranceNum\" : null,\n\t\t\t\"AccrualCriteria\" : \"tNO\",\n\t\t\t\"WTCode\" : null,\n\t\t\t\"BillToBuildingFloorRoom\" : null,\n\t\t\t\"DownPaymentClearAct\" : \"\",\n\t\t\t\"ChannelBP\" : null,\n\t\t\t\"DefaultTechnician\" : null,\n\t\t\t\"BilltoDefault\" : \"de Facturacion\",\n\t\t\t\"CustomerBillofExchangDisc\" : null,\n\t\t\t\"Territory\" : null,\n\t\t\t\"ShipToBuildingFloorRoom\" : null,\n\t\t\t\"CustomerBillofExchangPres\" : null,\n\t\t\t\"ProjectCode\" : null,\n\t\t\t\"VatGroupLatinAmerica\" : null,\n\t\t\t\"DunningTerm\" : null,\n\t\t\t\"Website\" : null,\n\t\t\t\"OtherReceivablePayable\" : null,\n\t\t\t\"BillofExchangeonCollection\" : null,\n\t\t\t\"CompanyPrivate\" : \"cCompany\",\n\t\t\t\"LanguageCode\" : 25,\n\t\t\t\"UnpaidBillofExchange\" : null,\n\t\t\t\"WithholdingTaxDeductionGroup\" : -1,\n\t\t\t\"ClosingDateProcedureNumber\" : null,\n\t\t\t\"Profession\" : null,\n\t\t\t\"BankChargesAllocationCode\" : null,\n\t\t\t\"TaxRoundingRule\" : \"trr_CompanyDefault\",\n\t\t\t\"Properties1\" : \"tNO\",\n\t\t\t\"Properties2\" : \"tNO\",\n\t\t\t\"Properties3\" : \"tNO\",\n\t\t\t\"Properties4\" : \"tNO\",\n\t\t\t\"Properties5\" : \"tNO\",\n\t\t\t\"Properties6\" : \"tNO\",\n\t\t\t\"Properties7\" : \"tNO\",\n\t\t\t\"Properties8\" : \"tNO\",\n\t\t\t\"Properties9\" : \"tNO\",\n\t\t\t\"Properties10\" : \"tNO\",\n\t\t\t\"Properties11\" : \"tNO\",\n\t\t\t\"Properties12\" : \"tNO\",\n\t\t\t\"Properties13\" : \"tNO\",\n\t\t\t\"Properties14\" : \"tNO\",\n\t\t\t\"Properties15\" : \"tNO\",\n\t\t\t\"Properties16\" : \"tNO\",\n\t\t\t\"Properties17\" : \"tNO\",\n\t\t\t\"Properties18\" : \"tNO\",\n\t\t\t\"Properties19\" : \"tNO\",\n\t\t\t\"Properties20\" : \"tNO\",\n\t\t\t\"Properties21\" : \"tNO\",\n\t\t\t\"Properties22\" : \"tNO\",\n\t\t\t\"Properties23\" : \"tNO\",\n\t\t\t\"Properties24\" : \"tNO\",\n\t\t\t\"Properties25\" : \"tNO\",\n\t\t\t\"Properties26\" : \"tNO\",\n\t\t\t\"Properties27\" : \"tNO\",\n\t\t\t\"Properties28\" : \"tNO\",\n\t\t\t\"Properties29\" : \"tNO\",\n\t\t\t\"Properties30\" : \"tNO\",\n\t\t\t\"Properties31\" : \"tNO\",\n\t\t\t\"Properties32\" : \"tNO\",\n\t\t\t\"Properties33\" : \"tNO\",\n\t\t\t\"Properties34\" : \"tNO\",\n\t\t\t\"Properties35\" : \"tNO\",\n\t\t\t\"Properties36\" : \"tNO\",\n\t\t\t\"Properties37\" : \"tNO\",\n\t\t\t\"Properties38\" : \"tNO\",\n\t\t\t\"Properties39\" : \"tNO\",\n\t\t\t\"Properties40\" : \"tNO\",\n\t\t\t\"Properties41\" : \"tNO\",\n\t\t\t\"Properties42\" : \"tNO\",\n\t\t\t\"Properties43\" : \"tNO\",\n\t\t\t\"Properties44\" : \"tNO\",\n\t\t\t\"Properties45\" : \"tNO\",\n\t\t\t\"Properties46\" : \"tNO\",\n\t\t\t\"Properties47\" : \"tNO\",\n\t\t\t\"Properties48\" : \"tNO\",\n\t\t\t\"Properties49\" : \"tNO\",\n\t\t\t\"Properties50\" : \"tNO\",\n\t\t\t\"Properties51\" : \"tNO\",\n\t\t\t\"Properties52\" : \"tNO\",\n\t\t\t\"Properties53\" : \"tNO\",\n\t\t\t\"Properties54\" : \"tNO\",\n\t\t\t\"Properties55\" : \"tNO\",\n\t\t\t\"Properties56\" : \"tNO\",\n\t\t\t\"Properties57\" : \"tNO\",\n\t\t\t\"Properties58\" : \"tNO\",\n\t\t\t\"Properties59\" : \"tNO\",\n\t\t\t\"Properties60\" : \"tNO\",\n\t\t\t\"Properties61\" : \"tNO\",\n\t\t\t\"Properties62\" : \"tNO\",\n\t\t\t\"Properties63\" : \"tNO\",\n\t\t\t\"Properties64\" : \"tNO\",\n\t\t\t\"CompanyRegistrationNumber\" : null,\n\t\t\t\"VerificationNumber\" : null,\n\t\t\t\"DiscountBaseObject\" : \"dgboNone\",\n\t\t\t\"DiscountRelations\" : \"dgrLowestDiscount\",\n\t\t\t\"TypeReport\" : \"atCompany\",\n\t\t\t\"ThresholdOverlook\" : \"tNO\",\n\t\t\t\"SurchargeOverlook\" : \"tNO\",\n\t\t\t\"Remark1\" : null,\n\t\t\t\"ConCerti\" : null,\n\t\t\t\"DownPaymentInterimAccount\" : null,\n\t\t\t\"OperationCode347\" : \"ocSalesOrServicesRevenues\",\n\t\t\t\"InsuranceOperation347\" : \"tNO\",\n\t\t\t\"HierarchicalDeduction\" : \"tNO\",\n\t\t\t\"ShaamGroup\" : \"sgServicesAndAsset\",\n\t\t\t\"WithholdingTaxCertified\" : \"tNO\",\n\t\t\t\"BookkeepingCertified\" : \"tNO\",\n\t\t\t\"PlanningGroup\" : null,\n\t\t\t\"Affiliate\" : \"tNO\",\n\t\t\t\"Industry\" : null,\n\t\t\t\"VatIDNum\" : null,\n\t\t\t\"DatevAccount\" : null,\n\t\t\t\"DatevFirstDataEntry\" : \"tYES\",\n\t\t\t\"UseShippedGoodsAccount\" : \"tNO\",\n\t\t\t\"GTSRegNo\" : null,\n\t\t\t\"GTSBankAccountNo\" : null,\n\t\t\t\"GTSBillingAddrTel\" : null,\n\t\t\t\"ETaxWebSite\" : null,\n\t\t\t\"HouseBankIBAN\" : \"\",\n\t\t\t\"VATRegistrationNumber\" : null,\n\t\t\t\"RepresentativeName\" : null,\n\t\t\t\"IndustryType\" : null,\n\t\t\t\"BusinessType\" : null,\n\t\t\t\"Series\" : 942,\n\t\t\t\"AutomaticPosting\" : \"apNo\",\n\t\t\t\"InterestAccount\" : null,\n\t\t\t\"FeeAccount\" : null,\n\t\t\t\"CampaignNumber\" : null,\n\t\t\t\"AliasName\" : null,\n\t\t\t\"DefaultBlanketAgreementNumber\" : null,\n\t\t\t\"EffectiveDiscount\" : \"dgrLowestDiscount\",\n\t\t\t\"NoDiscounts\" : \"tNO\",\n\t\t\t\"EffectivePrice\" : \"epDefaultPriority\",\n\t\t\t\"EffectivePriceConsidersPriceBeforeDiscount\" : \"tNO\",\n\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\"EDISenderID\" : null,\n\t\t\t\"EDIRecipientID\" : null,\n\t\t\t\"ResidenNumber\" : \"rntSpanishFiscalID\",\n\t\t\t\"RelationshipCode\" : null,\n\t\t\t\"RelationshipDateFrom\" : null,\n\t\t\t\"RelationshipDateTill\" : null,\n\t\t\t\"UnifiedFederalTaxID\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"TypeOfOperation\" : null,\n\t\t\t\"EndorsableChecksFromBP\" : \"tYES\",\n\t\t\t\"AcceptsEndorsedChecks\" : \"tNO\",\n\t\t\t\"OwnerCode\" : null,\n\t\t\t\"BlockSendingMarketingContent\" : \"tNO\",\n\t\t\t\"AgentCode\" : null,\n\t\t\t\"PriceMode\" : null,\n\t\t\t\"EDocGenerationType\" : null,\n\t\t\t\"EDocStreet\" : null,\n\t\t\t\"EDocStreetNumber\" : null,\n\t\t\t\"EDocBuildingNumber\" : null,\n\t\t\t\"EDocZipCode\" : null,\n\t\t\t\"EDocCity\" : null,\n\t\t\t\"EDocCountry\" : null,\n\t\t\t\"EDocDistrict\" : null,\n\t\t\t\"EDocRepresentativeFirstName\" : null,\n\t\t\t\"EDocRepresentativeSurname\" : null,\n\t\t\t\"EDocRepresentativeCompany\" : null,\n\t\t\t\"EDocRepresentativeFiscalCode\" : null,\n\t\t\t\"EDocRepresentativeAdditionalId\" : null,\n\t\t\t\"EDocPECAddress\" : null,\n\t\t\t\"IPACodeForPA\" : null,\n\t\t\t\"UpdateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"21:20:50\",\n\t\t\t\"ExemptionMaxAmountValidationType\" : \"emaIndividual\",\n\t\t\t\"ECommerceMerchantID\" : null,\n\t\t\t\"UseBillToAddrToDetermineTax\" : \"tNO\",\n\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"18:32:43\",\n\t\t\t\"DefaultTransporterEntry\" : null,\n\t\t\t\"DefaultTransporterLineNumber\" : null,\n\t\t\t\"FCERelevant\" : \"tNO\",\n\t\t\t\"FCEValidateBaseDelivery\" : \"tNO\",\n\t\t\t\"MainUsage\" : null,\n\t\t\t\"EBooksVATExemptionCause\" : null,\n\t\t\t\"LegalText\" : null,\n\t\t\t\"DataVersion\" : 4,\n\t\t\t\"ExchangeRateForIncomingPayment\" : \"tYES\",\n\t\t\t\"ExchangeRateForOutgoingPayment\" : \"tYES\",\n\t\t\t\"CertificateDetails\" : null,\n\t\t\t\"DefaultCurrency\" : null,\n\t\t\t\"EORINumber\" : null,\n\t\t\t\"FCEAsPaymentMeans\" : \"tNO\",\n\t\t\t\"NotRelevantForMonthlyInvoice\" : \"tNO\",\n\t\t\t\"U_BENF\" : null,\n\t\t\t\"U_CBRT\" : \"S\",\n\t\t\t\"U_CO_RENT\" : null,\n\t\t\t\"U_CRID\" : \"IDENTIFICACION_TRIBUTARIA\",\n\t\t\t\"U_CRSI\" : \"contribuyente\",\n\t\t\t\"U_MDP\" : \"6\",\n\t\t\t\"U_NIDEN\" : null,\n\t\t\t\"U_PCFV\" : null,\n\t\t\t\"U_PRI\" : null,\n\t\t\t\"U_REPL\" : null,\n\t\t\t\"U_RRIV\" : \"S\",\n\t\t\t\"U_TIPCONT\" : 1,\n\t\t\t\"U_EPY_TSNF\" : null,\n\t\t\t\"U_NCCO\" : null,\n\t\t\t\"U_NCON\" : null,\n\t\t\t\"U_OTRO\" : null,\n\t\t\t\"U_TCON\" : \"1\",\n\t\t\t\"U_EXX_FE_TipoOperacion\" : \"2\",\n\t\t\t\"U_TipoTrato\" : \"N\",\n\t\t\t\"U_Especialidad\" : null,\n\t\t\t\"U_Cargo\" : null,\n\t\t\t\"U_Proyecto\" : null,\n\t\t\t\"U_AntTope\" : 0.0,\n\t\t\t\"U_RUT\" : null,\n\t\t\t\"U_PrcRet\" : null,\n\t\t\t\"U_PrcAnt\" : null,\n\t\t\t\"ElectronicProtocols\" : [],\n\t\t\t\"BPAddresses\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"AddressName\" : \"de Facturacion\",\n\t\t\t\t\t\"Street\" : \"-\",\n\t\t\t\t\t\"Block\" : null,\n\t\t\t\t\t\"ZipCode\" : null,\n\t\t\t\t\t\"City\" : null,\n\t\t\t\t\t\"County\" : null,\n\t\t\t\t\t\"Country\" : \"PY\",\n\t\t\t\t\t\"State\" : null,\n\t\t\t\t\t\"FederalTaxID\" : null,\n\t\t\t\t\t\"TaxCode\" : null,\n\t\t\t\t\t\"BuildingFloorRoom\" : null,\n\t\t\t\t\t\"AddressType\" : \"bo_BillTo\",\n\t\t\t\t\t\"AddressName2\" : null,\n\t\t\t\t\t\"AddressName3\" : null,\n\t\t\t\t\t\"TypeOfAddress\" : null,\n\t\t\t\t\t\"StreetNo\" : \"0\",\n\t\t\t\t\t\"BPCode\" : \"CLIE000646\",\n\t\t\t\t\t\"RowNum\" : 0,\n\t\t\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\t\t\"Nationality\" : null,\n\t\t\t\t\t\"TaxOffice\" : null,\n\t\t\t\t\t\"GSTIN\" : null,\n\t\t\t\t\t\"GstType\" : null,\n\t\t\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\t\t\"CreateTime\" : \"21:20:50\",\n\t\t\t\t\t\"MYFType\" : null,\n\t\t\t\t\t\"TaasEnabled\" : \"tYES\",\n\t\t\t\t\t\"U_EXX_FE_DEPT\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_DIST\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BALO\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BARR\" : null\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"ContactEmployees\" : [],\n\t\t\t\"BPAccountReceivablePaybleCollection\" : [],\n\t\t\t\"BPPaymentMethods\" : [],\n\t\t\t\"BPWithholdingTaxCollection\" : [],\n\t\t\t\"BPPaymentDates\" : [],\n\t\t\t\"BPBranchAssignment\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"BPCode\" : \"CLIE000646\",\n\t\t\t\t\t\"BPLID\" : 1,\n\t\t\t\t\t\"DisabledForBP\" : \"tNO\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"BPBankAccounts\" : [],\n\t\t\t\"BPFiscalTaxIDCollection\" : [],\n\t\t\t\"DiscountGroups\" : [],\n\t\t\t\"BPIntrastatExtension\" : {},\n\t\t\t\"BPBlockSendingMarketingContents\" : [],\n\t\t\t\"BPCurrenciesCollection\" : []\n\t\t},\n\t\t{\n\t\t\t\"@odata.etag\" : \"W/\\\"1B6453892473A467D07372D45EB05ABC2031647A\\\"\",\n\t\t\t\"CardCode\" : \"CLIE000647\",\n\t\t\t\"CardName\" : \"DEP. ARACELI\",\n\t\t\t\"CardType\" : \"cCustomer\",\n\t\t\t\"GroupCode\" : 100,\n\t\t\t\"Address\" : \"-\",\n\t\t\t\"ZipCode\" : null,\n\t\t\t\"MailAddress\" : null,\n\t\t\t\"MailZipCode\" : null,\n\t\t\t\"Phone1\" : null,\n\t\t\t\"Phone2\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"ContactPerson\" : null,\n\t\t\t\"Notes\" : null,\n\t\t\t\"PayTermsGrpCode\" : -1,\n\t\t\t\"CreditLimit\" : 0.0,\n\t\t\t\"MaxCommitment\" : 0.0,\n\t\t\t\"DiscountPercent\" : 0.0,\n\t\t\t\"VatLiable\" : \"vLiable\",\n\t\t\t\"FederalTaxID\" : \"2092380-5\",\n\t\t\t\"DeductibleAtSource\" : \"tNO\",\n\t\t\t\"DeductionPercent\" : 0.0,\n\t\t\t\"DeductionValidUntil\" : null,\n\t\t\t\"PriceListNum\" : 1,\n\t\t\t\"IntrestRatePercent\" : 0.0,\n\t\t\t\"CommissionPercent\" : 0.0,\n\t\t\t\"CommissionGroupCode\" : 0,\n\t\t\t\"FreeText\" : null,\n\t\t\t\"SalesPersonCode\" : -1,\n\t\t\t\"Currency\" : \"GS\",\n\t\t\t\"RateDiffAccount\" : null,\n\t\t\t\"Cellular\" : null,\n\t\t\t\"AvarageLate\" : null,\n\t\t\t\"City\" : null,\n\t\t\t\"County\" : null,\n\t\t\t\"Country\" : \"PY\",\n\t\t\t\"MailCity\" : null,\n\t\t\t\"MailCounty\" : null,\n\t\t\t\"MailCountry\" : null,\n\t\t\t\"EmailAddress\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"DefaultAccount\" : null,\n\t\t\t\"DefaultBranch\" : null,\n\t\t\t\"DefaultBankCode\" : \"-1\",\n\t\t\t\"AdditionalID\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"FatherCard\" : null,\n\t\t\t\"CardForeignName\" : \"DEP. ARACELI\",\n\t\t\t\"FatherType\" : \"cPayments_sum\",\n\t\t\t\"DeductionOffice\" : null,\n\t\t\t\"ExportCode\" : null,\n\t\t\t\"MinIntrest\" : 0.0,\n\t\t\t\"CurrentAccountBalance\" : 0.0,\n\t\t\t\"OpenDeliveryNotesBalance\" : 0.0,\n\t\t\t\"OpenOrdersBalance\" : 0.0,\n\t\t\t\"OpenChecksBalance\" : 0.0,\n\t\t\t\"VatGroup\" : null,\n\t\t\t\"ShippingType\" : null,\n\t\t\t\"Password\" : null,\n\t\t\t\"Indicator\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CreditCardCode\" : -1,\n\t\t\t\"CreditCardNum\" : null,\n\t\t\t\"CreditCardExpiration\" : null,\n\t\t\t\"DebitorAccount\" : \"********\",\n\t\t\t\"OpenOpportunities\" : null,\n\t\t\t\"Valid\" : \"tYES\",\n\t\t\t\"ValidFrom\" : null,\n\t\t\t\"ValidTo\" : null,\n\t\t\t\"ValidRemarks\" : null,\n\t\t\t\"Frozen\" : \"tNO\",\n\t\t\t\"FrozenFrom\" : null,\n\t\t\t\"FrozenTo\" : null,\n\t\t\t\"FrozenRemarks\" : null,\n\t\t\t\"Block\" : null,\n\t\t\t\"BillToState\" : null,\n\t\t\t\"ShipToState\" : null,\n\t\t\t\"ExemptNum\" : null,\n\t\t\t\"Priority\" : -1,\n\t\t\t\"FormCode1099\" : null,\n\t\t\t\"Box1099\" : null,\n\t\t\t\"PeymentMethodCode\" : null,\n\t\t\t\"BackOrder\" : \"tYES\",\n\t\t\t\"PartialDelivery\" : \"tYES\",\n\t\t\t\"BlockDunning\" : \"tNO\",\n\t\t\t\"BankCountry\" : \"PY\",\n\t\t\t\"HouseBank\" : null,\n\t\t\t\"HouseBankCountry\" : \"PY\",\n\t\t\t\"HouseBankAccount\" : null,\n\t\t\t\"ShipToDefault\" : null,\n\t\t\t\"DunningLevel\" : null,\n\t\t\t\"DunningDate\" : null,\n\t\t\t\"CollectionAuthorization\" : \"tNO\",\n\t\t\t\"DME\" : null,\n\t\t\t\"InstructionKey\" : null,\n\t\t\t\"SinglePayment\" : \"tNO\",\n\t\t\t\"ISRBillerID\" : null,\n\t\t\t\"PaymentBlock\" : \"tNO\",\n\t\t\t\"ReferenceDetails\" : null,\n\t\t\t\"HouseBankBranch\" : null,\n\t\t\t\"OwnerIDNumber\" : null,\n\t\t\t\"PaymentBlockDescription\" : -1,\n\t\t\t\"TaxExemptionLetterNum\" : null,\n\t\t\t\"MaxAmountOfExemption\" : 0.0,\n\t\t\t\"ExemptionValidityDateFrom\" : null,\n\t\t\t\"ExemptionValidityDateTo\" : null,\n\t\t\t\"LinkedBusinessPartner\" : null,\n\t\t\t\"LastMultiReconciliationNum\" : null,\n\t\t\t\"DeferredTax\" : \"tNO\",\n\t\t\t\"Equalization\" : \"tNO\",\n\t\t\t\"SubjectToWithholdingTax\" : \"boNO\",\n\t\t\t\"CertificateNumber\" : null,\n\t\t\t\"ExpirationDate\" : null,\n\t\t\t\"NationalInsuranceNum\" : null,\n\t\t\t\"AccrualCriteria\" : \"tNO\",\n\t\t\t\"WTCode\" : null,\n\t\t\t\"BillToBuildingFloorRoom\" : null,\n\t\t\t\"DownPaymentClearAct\" : \"\",\n\t\t\t\"ChannelBP\" : null,\n\t\t\t\"DefaultTechnician\" : null,\n\t\t\t\"BilltoDefault\" : \"de Facturacion\",\n\t\t\t\"CustomerBillofExchangDisc\" : null,\n\t\t\t\"Territory\" : null,\n\t\t\t\"ShipToBuildingFloorRoom\" : null,\n\t\t\t\"CustomerBillofExchangPres\" : null,\n\t\t\t\"ProjectCode\" : null,\n\t\t\t\"VatGroupLatinAmerica\" : null,\n\t\t\t\"DunningTerm\" : null,\n\t\t\t\"Website\" : null,\n\t\t\t\"OtherReceivablePayable\" : null,\n\t\t\t\"BillofExchangeonCollection\" : null,\n\t\t\t\"CompanyPrivate\" : \"cCompany\",\n\t\t\t\"LanguageCode\" : 25,\n\t\t\t\"UnpaidBillofExchange\" : null,\n\t\t\t\"WithholdingTaxDeductionGroup\" : -1,\n\t\t\t\"ClosingDateProcedureNumber\" : null,\n\t\t\t\"Profession\" : null,\n\t\t\t\"BankChargesAllocationCode\" : null,\n\t\t\t\"TaxRoundingRule\" : \"trr_CompanyDefault\",\n\t\t\t\"Properties1\" : \"tNO\",\n\t\t\t\"Properties2\" : \"tNO\",\n\t\t\t\"Properties3\" : \"tNO\",\n\t\t\t\"Properties4\" : \"tNO\",\n\t\t\t\"Properties5\" : \"tNO\",\n\t\t\t\"Properties6\" : \"tNO\",\n\t\t\t\"Properties7\" : \"tNO\",\n\t\t\t\"Properties8\" : \"tNO\",\n\t\t\t\"Properties9\" : \"tNO\",\n\t\t\t\"Properties10\" : \"tNO\",\n\t\t\t\"Properties11\" : \"tNO\",\n\t\t\t\"Properties12\" : \"tNO\",\n\t\t\t\"Properties13\" : \"tNO\",\n\t\t\t\"Properties14\" : \"tNO\",\n\t\t\t\"Properties15\" : \"tNO\",\n\t\t\t\"Properties16\" : \"tNO\",\n\t\t\t\"Properties17\" : \"tNO\",\n\t\t\t\"Properties18\" : \"tNO\",\n\t\t\t\"Properties19\" : \"tNO\",\n\t\t\t\"Properties20\" : \"tNO\",\n\t\t\t\"Properties21\" : \"tNO\",\n\t\t\t\"Properties22\" : \"tNO\",\n\t\t\t\"Properties23\" : \"tNO\",\n\t\t\t\"Properties24\" : \"tNO\",\n\t\t\t\"Properties25\" : \"tNO\",\n\t\t\t\"Properties26\" : \"tNO\",\n\t\t\t\"Properties27\" : \"tNO\",\n\t\t\t\"Properties28\" : \"tNO\",\n\t\t\t\"Properties29\" : \"tNO\",\n\t\t\t\"Properties30\" : \"tNO\",\n\t\t\t\"Properties31\" : \"tNO\",\n\t\t\t\"Properties32\" : \"tNO\",\n\t\t\t\"Properties33\" : \"tNO\",\n\t\t\t\"Properties34\" : \"tNO\",\n\t\t\t\"Properties35\" : \"tNO\",\n\t\t\t\"Properties36\" : \"tNO\",\n\t\t\t\"Properties37\" : \"tNO\",\n\t\t\t\"Properties38\" : \"tNO\",\n\t\t\t\"Properties39\" : \"tNO\",\n\t\t\t\"Properties40\" : \"tNO\",\n\t\t\t\"Properties41\" : \"tNO\",\n\t\t\t\"Properties42\" : \"tNO\",\n\t\t\t\"Properties43\" : \"tNO\",\n\t\t\t\"Properties44\" : \"tNO\",\n\t\t\t\"Properties45\" : \"tNO\",\n\t\t\t\"Properties46\" : \"tNO\",\n\t\t\t\"Properties47\" : \"tNO\",\n\t\t\t\"Properties48\" : \"tNO\",\n\t\t\t\"Properties49\" : \"tNO\",\n\t\t\t\"Properties50\" : \"tNO\",\n\t\t\t\"Properties51\" : \"tNO\",\n\t\t\t\"Properties52\" : \"tNO\",\n\t\t\t\"Properties53\" : \"tNO\",\n\t\t\t\"Properties54\" : \"tNO\",\n\t\t\t\"Properties55\" : \"tNO\",\n\t\t\t\"Properties56\" : \"tNO\",\n\t\t\t\"Properties57\" : \"tNO\",\n\t\t\t\"Properties58\" : \"tNO\",\n\t\t\t\"Properties59\" : \"tNO\",\n\t\t\t\"Properties60\" : \"tNO\",\n\t\t\t\"Properties61\" : \"tNO\",\n\t\t\t\"Properties62\" : \"tNO\",\n\t\t\t\"Properties63\" : \"tNO\",\n\t\t\t\"Properties64\" : \"tNO\",\n\t\t\t\"CompanyRegistrationNumber\" : null,\n\t\t\t\"VerificationNumber\" : null,\n\t\t\t\"DiscountBaseObject\" : \"dgboNone\",\n\t\t\t\"DiscountRelations\" : \"dgrLowestDiscount\",\n\t\t\t\"TypeReport\" : \"atCompany\",\n\t\t\t\"ThresholdOverlook\" : \"tNO\",\n\t\t\t\"SurchargeOverlook\" : \"tNO\",\n\t\t\t\"Remark1\" : null,\n\t\t\t\"ConCerti\" : null,\n\t\t\t\"DownPaymentInterimAccount\" : null,\n\t\t\t\"OperationCode347\" : \"ocSalesOrServicesRevenues\",\n\t\t\t\"InsuranceOperation347\" : \"tNO\",\n\t\t\t\"HierarchicalDeduction\" : \"tNO\",\n\t\t\t\"ShaamGroup\" : \"sgServicesAndAsset\",\n\t\t\t\"WithholdingTaxCertified\" : \"tNO\",\n\t\t\t\"BookkeepingCertified\" : \"tNO\",\n\t\t\t\"PlanningGroup\" : null,\n\t\t\t\"Affiliate\" : \"tNO\",\n\t\t\t\"Industry\" : null,\n\t\t\t\"VatIDNum\" : null,\n\t\t\t\"DatevAccount\" : null,\n\t\t\t\"DatevFirstDataEntry\" : \"tYES\",\n\t\t\t\"UseShippedGoodsAccount\" : \"tNO\",\n\t\t\t\"GTSRegNo\" : null,\n\t\t\t\"GTSBankAccountNo\" : null,\n\t\t\t\"GTSBillingAddrTel\" : null,\n\t\t\t\"ETaxWebSite\" : null,\n\t\t\t\"HouseBankIBAN\" : \"\",\n\t\t\t\"VATRegistrationNumber\" : null,\n\t\t\t\"RepresentativeName\" : null,\n\t\t\t\"IndustryType\" : null,\n\t\t\t\"BusinessType\" : null,\n\t\t\t\"Series\" : 942,\n\t\t\t\"AutomaticPosting\" : \"apNo\",\n\t\t\t\"InterestAccount\" : null,\n\t\t\t\"FeeAccount\" : null,\n\t\t\t\"CampaignNumber\" : null,\n\t\t\t\"AliasName\" : null,\n\t\t\t\"DefaultBlanketAgreementNumber\" : null,\n\t\t\t\"EffectiveDiscount\" : \"dgrLowestDiscount\",\n\t\t\t\"NoDiscounts\" : \"tNO\",\n\t\t\t\"EffectivePrice\" : \"epDefaultPriority\",\n\t\t\t\"EffectivePriceConsidersPriceBeforeDiscount\" : \"tNO\",\n\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\"EDISenderID\" : null,\n\t\t\t\"EDIRecipientID\" : null,\n\t\t\t\"ResidenNumber\" : \"rntSpanishFiscalID\",\n\t\t\t\"RelationshipCode\" : null,\n\t\t\t\"RelationshipDateFrom\" : null,\n\t\t\t\"RelationshipDateTill\" : null,\n\t\t\t\"UnifiedFederalTaxID\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"TypeOfOperation\" : null,\n\t\t\t\"EndorsableChecksFromBP\" : \"tYES\",\n\t\t\t\"AcceptsEndorsedChecks\" : \"tNO\",\n\t\t\t\"OwnerCode\" : null,\n\t\t\t\"BlockSendingMarketingContent\" : \"tNO\",\n\t\t\t\"AgentCode\" : null,\n\t\t\t\"PriceMode\" : null,\n\t\t\t\"EDocGenerationType\" : null,\n\t\t\t\"EDocStreet\" : null,\n\t\t\t\"EDocStreetNumber\" : null,\n\t\t\t\"EDocBuildingNumber\" : null,\n\t\t\t\"EDocZipCode\" : null,\n\t\t\t\"EDocCity\" : null,\n\t\t\t\"EDocCountry\" : null,\n\t\t\t\"EDocDistrict\" : null,\n\t\t\t\"EDocRepresentativeFirstName\" : null,\n\t\t\t\"EDocRepresentativeSurname\" : null,\n\t\t\t\"EDocRepresentativeCompany\" : null,\n\t\t\t\"EDocRepresentativeFiscalCode\" : null,\n\t\t\t\"EDocRepresentativeAdditionalId\" : null,\n\t\t\t\"EDocPECAddress\" : null,\n\t\t\t\"IPACodeForPA\" : null,\n\t\t\t\"UpdateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"21:20:51\",\n\t\t\t\"ExemptionMaxAmountValidationType\" : \"emaIndividual\",\n\t\t\t\"ECommerceMerchantID\" : null,\n\t\t\t\"UseBillToAddrToDetermineTax\" : \"tNO\",\n\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"18:32:43\",\n\t\t\t\"DefaultTransporterEntry\" : null,\n\t\t\t\"DefaultTransporterLineNumber\" : null,\n\t\t\t\"FCERelevant\" : \"tNO\",\n\t\t\t\"FCEValidateBaseDelivery\" : \"tNO\",\n\t\t\t\"MainUsage\" : null,\n\t\t\t\"EBooksVATExemptionCause\" : null,\n\t\t\t\"LegalText\" : null,\n\t\t\t\"DataVersion\" : 4,\n\t\t\t\"ExchangeRateForIncomingPayment\" : \"tYES\",\n\t\t\t\"ExchangeRateForOutgoingPayment\" : \"tYES\",\n\t\t\t\"CertificateDetails\" : null,\n\t\t\t\"DefaultCurrency\" : null,\n\t\t\t\"EORINumber\" : null,\n\t\t\t\"FCEAsPaymentMeans\" : \"tNO\",\n\t\t\t\"NotRelevantForMonthlyInvoice\" : \"tNO\",\n\t\t\t\"U_BENF\" : null,\n\t\t\t\"U_CBRT\" : \"S\",\n\t\t\t\"U_CO_RENT\" : null,\n\t\t\t\"U_CRID\" : \"IDENTIFICACION_TRIBUTARIA\",\n\t\t\t\"U_CRSI\" : \"contribuyente\",\n\t\t\t\"U_MDP\" : \"6\",\n\t\t\t\"U_NIDEN\" : null,\n\t\t\t\"U_PCFV\" : null,\n\t\t\t\"U_PRI\" : null,\n\t\t\t\"U_REPL\" : null,\n\t\t\t\"U_RRIV\" : \"S\",\n\t\t\t\"U_TIPCONT\" : 1,\n\t\t\t\"U_EPY_TSNF\" : null,\n\t\t\t\"U_NCCO\" : null,\n\t\t\t\"U_NCON\" : null,\n\t\t\t\"U_OTRO\" : null,\n\t\t\t\"U_TCON\" : \"1\",\n\t\t\t\"U_EXX_FE_TipoOperacion\" : \"2\",\n\t\t\t\"U_TipoTrato\" : \"N\",\n\t\t\t\"U_Especialidad\" : null,\n\t\t\t\"U_Cargo\" : null,\n\t\t\t\"U_Proyecto\" : null,\n\t\t\t\"U_AntTope\" : 0.0,\n\t\t\t\"U_RUT\" : null,\n\t\t\t\"U_PrcRet\" : null,\n\t\t\t\"U_PrcAnt\" : null,\n\t\t\t\"ElectronicProtocols\" : [],\n\t\t\t\"BPAddresses\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"AddressName\" : \"de Facturacion\",\n\t\t\t\t\t\"Street\" : \"-\",\n\t\t\t\t\t\"Block\" : null,\n\t\t\t\t\t\"ZipCode\" : null,\n\t\t\t\t\t\"City\" : null,\n\t\t\t\t\t\"County\" : null,\n\t\t\t\t\t\"Country\" : \"PY\",\n\t\t\t\t\t\"State\" : null,\n\t\t\t\t\t\"FederalTaxID\" : null,\n\t\t\t\t\t\"TaxCode\" : null,\n\t\t\t\t\t\"BuildingFloorRoom\" : null,\n\t\t\t\t\t\"AddressType\" : \"bo_BillTo\",\n\t\t\t\t\t\"AddressName2\" : null,\n\t\t\t\t\t\"AddressName3\" : null,\n\t\t\t\t\t\"TypeOfAddress\" : null,\n\t\t\t\t\t\"StreetNo\" : \"0\",\n\t\t\t\t\t\"BPCode\" : \"CLIE000647\",\n\t\t\t\t\t\"RowNum\" : 0,\n\t\t\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\t\t\"Nationality\" : null,\n\t\t\t\t\t\"TaxOffice\" : null,\n\t\t\t\t\t\"GSTIN\" : null,\n\t\t\t\t\t\"GstType\" : null,\n\t\t\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\t\t\"CreateTime\" : \"21:20:51\",\n\t\t\t\t\t\"MYFType\" : null,\n\t\t\t\t\t\"TaasEnabled\" : \"tYES\",\n\t\t\t\t\t\"U_EXX_FE_DEPT\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_DIST\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BALO\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BARR\" : null\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"ContactEmployees\" : [],\n\t\t\t\"BPAccountReceivablePaybleCollection\" : [],\n\t\t\t\"BPPaymentMethods\" : [],\n\t\t\t\"BPWithholdingTaxCollection\" : [],\n\t\t\t\"BPPaymentDates\" : [],\n\t\t\t\"BPBranchAssignment\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"BPCode\" : \"CLIE000647\",\n\t\t\t\t\t\"BPLID\" : 1,\n\t\t\t\t\t\"DisabledForBP\" : \"tNO\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"BPBankAccounts\" : [],\n\t\t\t\"BPFiscalTaxIDCollection\" : [],\n\t\t\t\"DiscountGroups\" : [],\n\t\t\t\"BPIntrastatExtension\" : {},\n\t\t\t\"BPBlockSendingMarketingContents\" : [],\n\t\t\t\"BPCurrenciesCollection\" : []\n\t\t},\n\t\t{\n\t\t\t\"@odata.etag\" : \"W/\\\"1B6453892473A467D07372D45EB05ABC2031647A\\\"\",\n\t\t\t\"CardCode\" : \"CLIE000650\",\n\t\t\t\"CardName\" : \"DEP. SORIA.\",\n\t\t\t\"CardType\" : \"cCustomer\",\n\t\t\t\"GroupCode\" : 100,\n\t\t\t\"Address\" : \"-\",\n\t\t\t\"ZipCode\" : null,\n\t\t\t\"MailAddress\" : null,\n\t\t\t\"MailZipCode\" : null,\n\t\t\t\"Phone1\" : null,\n\t\t\t\"Phone2\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"ContactPerson\" : null,\n\t\t\t\"Notes\" : null,\n\t\t\t\"PayTermsGrpCode\" : -1,\n\t\t\t\"CreditLimit\" : 0.0,\n\t\t\t\"MaxCommitment\" : 0.0,\n\t\t\t\"DiscountPercent\" : 0.0,\n\t\t\t\"VatLiable\" : \"vLiable\",\n\t\t\t\"FederalTaxID\" : \"1193507-3\",\n\t\t\t\"DeductibleAtSource\" : \"tNO\",\n\t\t\t\"DeductionPercent\" : 0.0,\n\t\t\t\"DeductionValidUntil\" : null,\n\t\t\t\"PriceListNum\" : 1,\n\t\t\t\"IntrestRatePercent\" : 0.0,\n\t\t\t\"CommissionPercent\" : 0.0,\n\t\t\t\"CommissionGroupCode\" : 0,\n\t\t\t\"FreeText\" : null,\n\t\t\t\"SalesPersonCode\" : -1,\n\t\t\t\"Currency\" : \"GS\",\n\t\t\t\"RateDiffAccount\" : null,\n\t\t\t\"Cellular\" : null,\n\t\t\t\"AvarageLate\" : null,\n\t\t\t\"City\" : null,\n\t\t\t\"County\" : null,\n\t\t\t\"Country\" : \"PY\",\n\t\t\t\"MailCity\" : null,\n\t\t\t\"MailCounty\" : null,\n\t\t\t\"MailCountry\" : null,\n\t\t\t\"EmailAddress\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"DefaultAccount\" : null,\n\t\t\t\"DefaultBranch\" : null,\n\t\t\t\"DefaultBankCode\" : \"-1\",\n\t\t\t\"AdditionalID\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"FatherCard\" : null,\n\t\t\t\"CardForeignName\" : \"DEP. SORIA.\",\n\t\t\t\"FatherType\" : \"cPayments_sum\",\n\t\t\t\"DeductionOffice\" : null,\n\t\t\t\"ExportCode\" : null,\n\t\t\t\"MinIntrest\" : 0.0,\n\t\t\t\"CurrentAccountBalance\" : 0.0,\n\t\t\t\"OpenDeliveryNotesBalance\" : 0.0,\n\t\t\t\"OpenOrdersBalance\" : 0.0,\n\t\t\t\"OpenChecksBalance\" : 0.0,\n\t\t\t\"VatGroup\" : null,\n\t\t\t\"ShippingType\" : null,\n\t\t\t\"Password\" : null,\n\t\t\t\"Indicator\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CreditCardCode\" : -1,\n\t\t\t\"CreditCardNum\" : null,\n\t\t\t\"CreditCardExpiration\" : null,\n\t\t\t\"DebitorAccount\" : \"********\",\n\t\t\t\"OpenOpportunities\" : null,\n\t\t\t\"Valid\" : \"tYES\",\n\t\t\t\"ValidFrom\" : null,\n\t\t\t\"ValidTo\" : null,\n\t\t\t\"ValidRemarks\" : null,\n\t\t\t\"Frozen\" : \"tNO\",\n\t\t\t\"FrozenFrom\" : null,\n\t\t\t\"FrozenTo\" : null,\n\t\t\t\"FrozenRemarks\" : null,\n\t\t\t\"Block\" : null,\n\t\t\t\"BillToState\" : null,\n\t\t\t\"ShipToState\" : null,\n\t\t\t\"ExemptNum\" : null,\n\t\t\t\"Priority\" : -1,\n\t\t\t\"FormCode1099\" : null,\n\t\t\t\"Box1099\" : null,\n\t\t\t\"PeymentMethodCode\" : null,\n\t\t\t\"BackOrder\" : \"tYES\",\n\t\t\t\"PartialDelivery\" : \"tYES\",\n\t\t\t\"BlockDunning\" : \"tNO\",\n\t\t\t\"BankCountry\" : \"PY\",\n\t\t\t\"HouseBank\" : null,\n\t\t\t\"HouseBankCountry\" : \"PY\",\n\t\t\t\"HouseBankAccount\" : null,\n\t\t\t\"ShipToDefault\" : null,\n\t\t\t\"DunningLevel\" : null,\n\t\t\t\"DunningDate\" : null,\n\t\t\t\"CollectionAuthorization\" : \"tNO\",\n\t\t\t\"DME\" : null,\n\t\t\t\"InstructionKey\" : null,\n\t\t\t\"SinglePayment\" : \"tNO\",\n\t\t\t\"ISRBillerID\" : null,\n\t\t\t\"PaymentBlock\" : \"tNO\",\n\t\t\t\"ReferenceDetails\" : null,\n\t\t\t\"HouseBankBranch\" : null,\n\t\t\t\"OwnerIDNumber\" : null,\n\t\t\t\"PaymentBlockDescription\" : -1,\n\t\t\t\"TaxExemptionLetterNum\" : null,\n\t\t\t\"MaxAmountOfExemption\" : 0.0,\n\t\t\t\"ExemptionValidityDateFrom\" : null,\n\t\t\t\"ExemptionValidityDateTo\" : null,\n\t\t\t\"LinkedBusinessPartner\" : null,\n\t\t\t\"LastMultiReconciliationNum\" : null,\n\t\t\t\"DeferredTax\" : \"tNO\",\n\t\t\t\"Equalization\" : \"tNO\",\n\t\t\t\"SubjectToWithholdingTax\" : \"boNO\",\n\t\t\t\"CertificateNumber\" : null,\n\t\t\t\"ExpirationDate\" : null,\n\t\t\t\"NationalInsuranceNum\" : null,\n\t\t\t\"AccrualCriteria\" : \"tNO\",\n\t\t\t\"WTCode\" : null,\n\t\t\t\"BillToBuildingFloorRoom\" : null,\n\t\t\t\"DownPaymentClearAct\" : \"\",\n\t\t\t\"ChannelBP\" : null,\n\t\t\t\"DefaultTechnician\" : null,\n\t\t\t\"BilltoDefault\" : \"de Facturacion\",\n\t\t\t\"CustomerBillofExchangDisc\" : null,\n\t\t\t\"Territory\" : null,\n\t\t\t\"ShipToBuildingFloorRoom\" : null,\n\t\t\t\"CustomerBillofExchangPres\" : null,\n\t\t\t\"ProjectCode\" : null,\n\t\t\t\"VatGroupLatinAmerica\" : null,\n\t\t\t\"DunningTerm\" : null,\n\t\t\t\"Website\" : null,\n\t\t\t\"OtherReceivablePayable\" : null,\n\t\t\t\"BillofExchangeonCollection\" : null,\n\t\t\t\"CompanyPrivate\" : \"cCompany\",\n\t\t\t\"LanguageCode\" : 25,\n\t\t\t\"UnpaidBillofExchange\" : null,\n\t\t\t\"WithholdingTaxDeductionGroup\" : -1,\n\t\t\t\"ClosingDateProcedureNumber\" : null,\n\t\t\t\"Profession\" : null,\n\t\t\t\"BankChargesAllocationCode\" : null,\n\t\t\t\"TaxRoundingRule\" : \"trr_CompanyDefault\",\n\t\t\t\"Properties1\" : \"tNO\",\n\t\t\t\"Properties2\" : \"tNO\",\n\t\t\t\"Properties3\" : \"tNO\",\n\t\t\t\"Properties4\" : \"tNO\",\n\t\t\t\"Properties5\" : \"tNO\",\n\t\t\t\"Properties6\" : \"tNO\",\n\t\t\t\"Properties7\" : \"tNO\",\n\t\t\t\"Properties8\" : \"tNO\",\n\t\t\t\"Properties9\" : \"tNO\",\n\t\t\t\"Properties10\" : \"tNO\",\n\t\t\t\"Properties11\" : \"tNO\",\n\t\t\t\"Properties12\" : \"tNO\",\n\t\t\t\"Properties13\" : \"tNO\",\n\t\t\t\"Properties14\" : \"tNO\",\n\t\t\t\"Properties15\" : \"tNO\",\n\t\t\t\"Properties16\" : \"tNO\",\n\t\t\t\"Properties17\" : \"tNO\",\n\t\t\t\"Properties18\" : \"tNO\",\n\t\t\t\"Properties19\" : \"tNO\",\n\t\t\t\"Properties20\" : \"tNO\",\n\t\t\t\"Properties21\" : \"tNO\",\n\t\t\t\"Properties22\" : \"tNO\",\n\t\t\t\"Properties23\" : \"tNO\",\n\t\t\t\"Properties24\" : \"tNO\",\n\t\t\t\"Properties25\" : \"tNO\",\n\t\t\t\"Properties26\" : \"tNO\",\n\t\t\t\"Properties27\" : \"tNO\",\n\t\t\t\"Properties28\" : \"tNO\",\n\t\t\t\"Properties29\" : \"tNO\",\n\t\t\t\"Properties30\" : \"tNO\",\n\t\t\t\"Properties31\" : \"tNO\",\n\t\t\t\"Properties32\" : \"tNO\",\n\t\t\t\"Properties33\" : \"tNO\",\n\t\t\t\"Properties34\" : \"tNO\",\n\t\t\t\"Properties35\" : \"tNO\",\n\t\t\t\"Properties36\" : \"tNO\",\n\t\t\t\"Properties37\" : \"tNO\",\n\t\t\t\"Properties38\" : \"tNO\",\n\t\t\t\"Properties39\" : \"tNO\",\n\t\t\t\"Properties40\" : \"tNO\",\n\t\t\t\"Properties41\" : \"tNO\",\n\t\t\t\"Properties42\" : \"tNO\",\n\t\t\t\"Properties43\" : \"tNO\",\n\t\t\t\"Properties44\" : \"tNO\",\n\t\t\t\"Properties45\" : \"tNO\",\n\t\t\t\"Properties46\" : \"tNO\",\n\t\t\t\"Properties47\" : \"tNO\",\n\t\t\t\"Properties48\" : \"tNO\",\n\t\t\t\"Properties49\" : \"tNO\",\n\t\t\t\"Properties50\" : \"tNO\",\n\t\t\t\"Properties51\" : \"tNO\",\n\t\t\t\"Properties52\" : \"tNO\",\n\t\t\t\"Properties53\" : \"tNO\",\n\t\t\t\"Properties54\" : \"tNO\",\n\t\t\t\"Properties55\" : \"tNO\",\n\t\t\t\"Properties56\" : \"tNO\",\n\t\t\t\"Properties57\" : \"tNO\",\n\t\t\t\"Properties58\" : \"tNO\",\n\t\t\t\"Properties59\" : \"tNO\",\n\t\t\t\"Properties60\" : \"tNO\",\n\t\t\t\"Properties61\" : \"tNO\",\n\t\t\t\"Properties62\" : \"tNO\",\n\t\t\t\"Properties63\" : \"tNO\",\n\t\t\t\"Properties64\" : \"tNO\",\n\t\t\t\"CompanyRegistrationNumber\" : null,\n\t\t\t\"VerificationNumber\" : null,\n\t\t\t\"DiscountBaseObject\" : \"dgboNone\",\n\t\t\t\"DiscountRelations\" : \"dgrLowestDiscount\",\n\t\t\t\"TypeReport\" : \"atCompany\",\n\t\t\t\"ThresholdOverlook\" : \"tNO\",\n\t\t\t\"SurchargeOverlook\" : \"tNO\",\n\t\t\t\"Remark1\" : null,\n\t\t\t\"ConCerti\" : null,\n\t\t\t\"DownPaymentInterimAccount\" : null,\n\t\t\t\"OperationCode347\" : \"ocSalesOrServicesRevenues\",\n\t\t\t\"InsuranceOperation347\" : \"tNO\",\n\t\t\t\"HierarchicalDeduction\" : \"tNO\",\n\t\t\t\"ShaamGroup\" : \"sgServicesAndAsset\",\n\t\t\t\"WithholdingTaxCertified\" : \"tNO\",\n\t\t\t\"BookkeepingCertified\" : \"tNO\",\n\t\t\t\"PlanningGroup\" : null,\n\t\t\t\"Affiliate\" : \"tNO\",\n\t\t\t\"Industry\" : null,\n\t\t\t\"VatIDNum\" : null,\n\t\t\t\"DatevAccount\" : null,\n\t\t\t\"DatevFirstDataEntry\" : \"tYES\",\n\t\t\t\"UseShippedGoodsAccount\" : \"tNO\",\n\t\t\t\"GTSRegNo\" : null,\n\t\t\t\"GTSBankAccountNo\" : null,\n\t\t\t\"GTSBillingAddrTel\" : null,\n\t\t\t\"ETaxWebSite\" : null,\n\t\t\t\"HouseBankIBAN\" : \"\",\n\t\t\t\"VATRegistrationNumber\" : null,\n\t\t\t\"RepresentativeName\" : null,\n\t\t\t\"IndustryType\" : null,\n\t\t\t\"BusinessType\" : null,\n\t\t\t\"Series\" : 942,\n\t\t\t\"AutomaticPosting\" : \"apNo\",\n\t\t\t\"InterestAccount\" : null,\n\t\t\t\"FeeAccount\" : null,\n\t\t\t\"CampaignNumber\" : null,\n\t\t\t\"AliasName\" : null,\n\t\t\t\"DefaultBlanketAgreementNumber\" : null,\n\t\t\t\"EffectiveDiscount\" : \"dgrLowestDiscount\",\n\t\t\t\"NoDiscounts\" : \"tNO\",\n\t\t\t\"EffectivePrice\" : \"epDefaultPriority\",\n\t\t\t\"EffectivePriceConsidersPriceBeforeDiscount\" : \"tNO\",\n\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\"EDISenderID\" : null,\n\t\t\t\"EDIRecipientID\" : null,\n\t\t\t\"ResidenNumber\" : \"rntSpanishFiscalID\",\n\t\t\t\"RelationshipCode\" : null,\n\t\t\t\"RelationshipDateFrom\" : null,\n\t\t\t\"RelationshipDateTill\" : null,\n\t\t\t\"UnifiedFederalTaxID\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"TypeOfOperation\" : null,\n\t\t\t\"EndorsableChecksFromBP\" : \"tYES\",\n\t\t\t\"AcceptsEndorsedChecks\" : \"tNO\",\n\t\t\t\"OwnerCode\" : null,\n\t\t\t\"BlockSendingMarketingContent\" : \"tNO\",\n\t\t\t\"AgentCode\" : null,\n\t\t\t\"PriceMode\" : null,\n\t\t\t\"EDocGenerationType\" : null,\n\t\t\t\"EDocStreet\" : null,\n\t\t\t\"EDocStreetNumber\" : null,\n\t\t\t\"EDocBuildingNumber\" : null,\n\t\t\t\"EDocZipCode\" : null,\n\t\t\t\"EDocCity\" : null,\n\t\t\t\"EDocCountry\" : null,\n\t\t\t\"EDocDistrict\" : null,\n\t\t\t\"EDocRepresentativeFirstName\" : null,\n\t\t\t\"EDocRepresentativeSurname\" : null,\n\t\t\t\"EDocRepresentativeCompany\" : null,\n\t\t\t\"EDocRepresentativeFiscalCode\" : null,\n\t\t\t\"EDocRepresentativeAdditionalId\" : null,\n\t\t\t\"EDocPECAddress\" : null,\n\t\t\t\"IPACodeForPA\" : null,\n\t\t\t\"UpdateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"21:20:52\",\n\t\t\t\"ExemptionMaxAmountValidationType\" : \"emaIndividual\",\n\t\t\t\"ECommerceMerchantID\" : null,\n\t\t\t\"UseBillToAddrToDetermineTax\" : \"tNO\",\n\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"18:32:43\",\n\t\t\t\"DefaultTransporterEntry\" : null,\n\t\t\t\"DefaultTransporterLineNumber\" : null,\n\t\t\t\"FCERelevant\" : \"tNO\",\n\t\t\t\"FCEValidateBaseDelivery\" : \"tNO\",\n\t\t\t\"MainUsage\" : null,\n\t\t\t\"EBooksVATExemptionCause\" : null,\n\t\t\t\"LegalText\" : null,\n\t\t\t\"DataVersion\" : 4,\n\t\t\t\"ExchangeRateForIncomingPayment\" : \"tYES\",\n\t\t\t\"ExchangeRateForOutgoingPayment\" : \"tYES\",\n\t\t\t\"CertificateDetails\" : null,\n\t\t\t\"DefaultCurrency\" : null,\n\t\t\t\"EORINumber\" : null,\n\t\t\t\"FCEAsPaymentMeans\" : \"tNO\",\n\t\t\t\"NotRelevantForMonthlyInvoice\" : \"tNO\",\n\t\t\t\"U_BENF\" : null,\n\t\t\t\"U_CBRT\" : \"S\",\n\t\t\t\"U_CO_RENT\" : null,\n\t\t\t\"U_CRID\" : \"IDENTIFICACION_TRIBUTARIA\",\n\t\t\t\"U_CRSI\" : \"contribuyente\",\n\t\t\t\"U_MDP\" : \"6\",\n\t\t\t\"U_NIDEN\" : null,\n\t\t\t\"U_PCFV\" : null,\n\t\t\t\"U_PRI\" : null,\n\t\t\t\"U_REPL\" : null,\n\t\t\t\"U_RRIV\" : \"S\",\n\t\t\t\"U_TIPCONT\" : 1,\n\t\t\t\"U_EPY_TSNF\" : null,\n\t\t\t\"U_NCCO\" : null,\n\t\t\t\"U_NCON\" : null,\n\t\t\t\"U_OTRO\" : null,\n\t\t\t\"U_TCON\" : \"1\",\n\t\t\t\"U_EXX_FE_TipoOperacion\" : \"2\",\n\t\t\t\"U_TipoTrato\" : \"N\",\n\t\t\t\"U_Especialidad\" : null,\n\t\t\t\"U_Cargo\" : null,\n\t\t\t\"U_Proyecto\" : null,\n\t\t\t\"U_AntTope\" : 0.0,\n\t\t\t\"U_RUT\" : null,\n\t\t\t\"U_PrcRet\" : null,\n\t\t\t\"U_PrcAnt\" : null,\n\t\t\t\"ElectronicProtocols\" : [],\n\t\t\t\"BPAddresses\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"AddressName\" : \"de Facturacion\",\n\t\t\t\t\t\"Street\" : \"-\",\n\t\t\t\t\t\"Block\" : null,\n\t\t\t\t\t\"ZipCode\" : null,\n\t\t\t\t\t\"City\" : null,\n\t\t\t\t\t\"County\" : null,\n\t\t\t\t\t\"Country\" : \"PY\",\n\t\t\t\t\t\"State\" : null,\n\t\t\t\t\t\"FederalTaxID\" : null,\n\t\t\t\t\t\"TaxCode\" : null,\n\t\t\t\t\t\"BuildingFloorRoom\" : null,\n\t\t\t\t\t\"AddressType\" : \"bo_BillTo\",\n\t\t\t\t\t\"AddressName2\" : null,\n\t\t\t\t\t\"AddressName3\" : null,\n\t\t\t\t\t\"TypeOfAddress\" : null,\n\t\t\t\t\t\"StreetNo\" : \"0\",\n\t\t\t\t\t\"BPCode\" : \"CLIE000650\",\n\t\t\t\t\t\"RowNum\" : 0,\n\t\t\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\t\t\"Nationality\" : null,\n\t\t\t\t\t\"TaxOffice\" : null,\n\t\t\t\t\t\"GSTIN\" : null,\n\t\t\t\t\t\"GstType\" : null,\n\t\t\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\t\t\"CreateTime\" : \"21:20:52\",\n\t\t\t\t\t\"MYFType\" : null,\n\t\t\t\t\t\"TaasEnabled\" : \"tYES\",\n\t\t\t\t\t\"U_EXX_FE_DEPT\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_DIST\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BALO\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BARR\" : null\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"ContactEmployees\" : [],\n\t\t\t\"BPAccountReceivablePaybleCollection\" : [],\n\t\t\t\"BPPaymentMethods\" : [],\n\t\t\t\"BPWithholdingTaxCollection\" : [],\n\t\t\t\"BPPaymentDates\" : [],\n\t\t\t\"BPBranchAssignment\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"BPCode\" : \"CLIE000650\",\n\t\t\t\t\t\"BPLID\" : 1,\n\t\t\t\t\t\"DisabledForBP\" : \"tNO\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"BPBankAccounts\" : [],\n\t\t\t\"BPFiscalTaxIDCollection\" : [],\n\t\t\t\"DiscountGroups\" : [],\n\t\t\t\"BPIntrastatExtension\" : {},\n\t\t\t\"BPBlockSendingMarketingContents\" : [],\n\t\t\t\"BPCurrenciesCollection\" : []\n\t\t},\n\t\t{\n\t\t\t\"@odata.etag\" : \"W/\\\"1B6453892473A467D07372D45EB05ABC2031647A\\\"\",\n\t\t\t\"CardCode\" : \"CLIE000651\",\n\t\t\t\"CardName\" : \"RUBEN ALEJANDRO LOPEZ AYALA\",\n\t\t\t\"CardType\" : \"cCustomer\",\n\t\t\t\"GroupCode\" : 100,\n\t\t\t\"Address\" : \"-\",\n\t\t\t\"ZipCode\" : null,\n\t\t\t\"MailAddress\" : null,\n\t\t\t\"MailZipCode\" : null,\n\t\t\t\"Phone1\" : null,\n\t\t\t\"Phone2\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"ContactPerson\" : null,\n\t\t\t\"Notes\" : null,\n\t\t\t\"PayTermsGrpCode\" : -1,\n\t\t\t\"CreditLimit\" : 0.0,\n\t\t\t\"MaxCommitment\" : 0.0,\n\t\t\t\"DiscountPercent\" : 0.0,\n\t\t\t\"VatLiable\" : \"vLiable\",\n\t\t\t\"FederalTaxID\" : \"3978776-1\",\n\t\t\t\"DeductibleAtSource\" : \"tNO\",\n\t\t\t\"DeductionPercent\" : 0.0,\n\t\t\t\"DeductionValidUntil\" : null,\n\t\t\t\"PriceListNum\" : 1,\n\t\t\t\"IntrestRatePercent\" : 0.0,\n\t\t\t\"CommissionPercent\" : 0.0,\n\t\t\t\"CommissionGroupCode\" : 0,\n\t\t\t\"FreeText\" : null,\n\t\t\t\"SalesPersonCode\" : -1,\n\t\t\t\"Currency\" : \"GS\",\n\t\t\t\"RateDiffAccount\" : null,\n\t\t\t\"Cellular\" : null,\n\t\t\t\"AvarageLate\" : null,\n\t\t\t\"City\" : null,\n\t\t\t\"County\" : null,\n\t\t\t\"Country\" : \"PY\",\n\t\t\t\"MailCity\" : null,\n\t\t\t\"MailCounty\" : null,\n\t\t\t\"MailCountry\" : null,\n\t\t\t\"EmailAddress\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"DefaultAccount\" : null,\n\t\t\t\"DefaultBranch\" : null,\n\t\t\t\"DefaultBankCode\" : \"-1\",\n\t\t\t\"AdditionalID\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"FatherCard\" : null,\n\t\t\t\"CardForeignName\" : \"RUBEN ALEJANDRO LOPEZ AYALA\",\n\t\t\t\"FatherType\" : \"cPayments_sum\",\n\t\t\t\"DeductionOffice\" : null,\n\t\t\t\"ExportCode\" : null,\n\t\t\t\"MinIntrest\" : 0.0,\n\t\t\t\"CurrentAccountBalance\" : 0.0,\n\t\t\t\"OpenDeliveryNotesBalance\" : 0.0,\n\t\t\t\"OpenOrdersBalance\" : 0.0,\n\t\t\t\"OpenChecksBalance\" : 0.0,\n\t\t\t\"VatGroup\" : null,\n\t\t\t\"ShippingType\" : null,\n\t\t\t\"Password\" : null,\n\t\t\t\"Indicator\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CreditCardCode\" : -1,\n\t\t\t\"CreditCardNum\" : null,\n\t\t\t\"CreditCardExpiration\" : null,\n\t\t\t\"DebitorAccount\" : \"********\",\n\t\t\t\"OpenOpportunities\" : null,\n\t\t\t\"Valid\" : \"tYES\",\n\t\t\t\"ValidFrom\" : null,\n\t\t\t\"ValidTo\" : null,\n\t\t\t\"ValidRemarks\" : null,\n\t\t\t\"Frozen\" : \"tNO\",\n\t\t\t\"FrozenFrom\" : null,\n\t\t\t\"FrozenTo\" : null,\n\t\t\t\"FrozenRemarks\" : null,\n\t\t\t\"Block\" : null,\n\t\t\t\"BillToState\" : null,\n\t\t\t\"ShipToState\" : null,\n\t\t\t\"ExemptNum\" : null,\n\t\t\t\"Priority\" : -1,\n\t\t\t\"FormCode1099\" : null,\n\t\t\t\"Box1099\" : null,\n\t\t\t\"PeymentMethodCode\" : null,\n\t\t\t\"BackOrder\" : \"tYES\",\n\t\t\t\"PartialDelivery\" : \"tYES\",\n\t\t\t\"BlockDunning\" : \"tNO\",\n\t\t\t\"BankCountry\" : \"PY\",\n\t\t\t\"HouseBank\" : null,\n\t\t\t\"HouseBankCountry\" : \"PY\",\n\t\t\t\"HouseBankAccount\" : null,\n\t\t\t\"ShipToDefault\" : null,\n\t\t\t\"DunningLevel\" : null,\n\t\t\t\"DunningDate\" : null,\n\t\t\t\"CollectionAuthorization\" : \"tNO\",\n\t\t\t\"DME\" : null,\n\t\t\t\"InstructionKey\" : null,\n\t\t\t\"SinglePayment\" : \"tNO\",\n\t\t\t\"ISRBillerID\" : null,\n\t\t\t\"PaymentBlock\" : \"tNO\",\n\t\t\t\"ReferenceDetails\" : null,\n\t\t\t\"HouseBankBranch\" : null,\n\t\t\t\"OwnerIDNumber\" : null,\n\t\t\t\"PaymentBlockDescription\" : -1,\n\t\t\t\"TaxExemptionLetterNum\" : null,\n\t\t\t\"MaxAmountOfExemption\" : 0.0,\n\t\t\t\"ExemptionValidityDateFrom\" : null,\n\t\t\t\"ExemptionValidityDateTo\" : null,\n\t\t\t\"LinkedBusinessPartner\" : null,\n\t\t\t\"LastMultiReconciliationNum\" : null,\n\t\t\t\"DeferredTax\" : \"tNO\",\n\t\t\t\"Equalization\" : \"tNO\",\n\t\t\t\"SubjectToWithholdingTax\" : \"boNO\",\n\t\t\t\"CertificateNumber\" : null,\n\t\t\t\"ExpirationDate\" : null,\n\t\t\t\"NationalInsuranceNum\" : null,\n\t\t\t\"AccrualCriteria\" : \"tNO\",\n\t\t\t\"WTCode\" : null,\n\t\t\t\"BillToBuildingFloorRoom\" : null,\n\t\t\t\"DownPaymentClearAct\" : \"\",\n\t\t\t\"ChannelBP\" : null,\n\t\t\t\"DefaultTechnician\" : null,\n\t\t\t\"BilltoDefault\" : \"de Facturacion\",\n\t\t\t\"CustomerBillofExchangDisc\" : null,\n\t\t\t\"Territory\" : null,\n\t\t\t\"ShipToBuildingFloorRoom\" : null,\n\t\t\t\"CustomerBillofExchangPres\" : null,\n\t\t\t\"ProjectCode\" : null,\n\t\t\t\"VatGroupLatinAmerica\" : null,\n\t\t\t\"DunningTerm\" : null,\n\t\t\t\"Website\" : null,\n\t\t\t\"OtherReceivablePayable\" : null,\n\t\t\t\"BillofExchangeonCollection\" : null,\n\t\t\t\"CompanyPrivate\" : \"cCompany\",\n\t\t\t\"LanguageCode\" : 25,\n\t\t\t\"UnpaidBillofExchange\" : null,\n\t\t\t\"WithholdingTaxDeductionGroup\" : -1,\n\t\t\t\"ClosingDateProcedureNumber\" : null,\n\t\t\t\"Profession\" : null,\n\t\t\t\"BankChargesAllocationCode\" : null,\n\t\t\t\"TaxRoundingRule\" : \"trr_CompanyDefault\",\n\t\t\t\"Properties1\" : \"tNO\",\n\t\t\t\"Properties2\" : \"tNO\",\n\t\t\t\"Properties3\" : \"tNO\",\n\t\t\t\"Properties4\" : \"tNO\",\n\t\t\t\"Properties5\" : \"tNO\",\n\t\t\t\"Properties6\" : \"tNO\",\n\t\t\t\"Properties7\" : \"tNO\",\n\t\t\t\"Properties8\" : \"tNO\",\n\t\t\t\"Properties9\" : \"tNO\",\n\t\t\t\"Properties10\" : \"tNO\",\n\t\t\t\"Properties11\" : \"tNO\",\n\t\t\t\"Properties12\" : \"tNO\",\n\t\t\t\"Properties13\" : \"tNO\",\n\t\t\t\"Properties14\" : \"tNO\",\n\t\t\t\"Properties15\" : \"tNO\",\n\t\t\t\"Properties16\" : \"tNO\",\n\t\t\t\"Properties17\" : \"tNO\",\n\t\t\t\"Properties18\" : \"tNO\",\n\t\t\t\"Properties19\" : \"tNO\",\n\t\t\t\"Properties20\" : \"tNO\",\n\t\t\t\"Properties21\" : \"tNO\",\n\t\t\t\"Properties22\" : \"tNO\",\n\t\t\t\"Properties23\" : \"tNO\",\n\t\t\t\"Properties24\" : \"tNO\",\n\t\t\t\"Properties25\" : \"tNO\",\n\t\t\t\"Properties26\" : \"tNO\",\n\t\t\t\"Properties27\" : \"tNO\",\n\t\t\t\"Properties28\" : \"tNO\",\n\t\t\t\"Properties29\" : \"tNO\",\n\t\t\t\"Properties30\" : \"tNO\",\n\t\t\t\"Properties31\" : \"tNO\",\n\t\t\t\"Properties32\" : \"tNO\",\n\t\t\t\"Properties33\" : \"tNO\",\n\t\t\t\"Properties34\" : \"tNO\",\n\t\t\t\"Properties35\" : \"tNO\",\n\t\t\t\"Properties36\" : \"tNO\",\n\t\t\t\"Properties37\" : \"tNO\",\n\t\t\t\"Properties38\" : \"tNO\",\n\t\t\t\"Properties39\" : \"tNO\",\n\t\t\t\"Properties40\" : \"tNO\",\n\t\t\t\"Properties41\" : \"tNO\",\n\t\t\t\"Properties42\" : \"tNO\",\n\t\t\t\"Properties43\" : \"tNO\",\n\t\t\t\"Properties44\" : \"tNO\",\n\t\t\t\"Properties45\" : \"tNO\",\n\t\t\t\"Properties46\" : \"tNO\",\n\t\t\t\"Properties47\" : \"tNO\",\n\t\t\t\"Properties48\" : \"tNO\",\n\t\t\t\"Properties49\" : \"tNO\",\n\t\t\t\"Properties50\" : \"tNO\",\n\t\t\t\"Properties51\" : \"tNO\",\n\t\t\t\"Properties52\" : \"tNO\",\n\t\t\t\"Properties53\" : \"tNO\",\n\t\t\t\"Properties54\" : \"tNO\",\n\t\t\t\"Properties55\" : \"tNO\",\n\t\t\t\"Properties56\" : \"tNO\",\n\t\t\t\"Properties57\" : \"tNO\",\n\t\t\t\"Properties58\" : \"tNO\",\n\t\t\t\"Properties59\" : \"tNO\",\n\t\t\t\"Properties60\" : \"tNO\",\n\t\t\t\"Properties61\" : \"tNO\",\n\t\t\t\"Properties62\" : \"tNO\",\n\t\t\t\"Properties63\" : \"tNO\",\n\t\t\t\"Properties64\" : \"tNO\",\n\t\t\t\"CompanyRegistrationNumber\" : null,\n\t\t\t\"VerificationNumber\" : null,\n\t\t\t\"DiscountBaseObject\" : \"dgboNone\",\n\t\t\t\"DiscountRelations\" : \"dgrLowestDiscount\",\n\t\t\t\"TypeReport\" : \"atCompany\",\n\t\t\t\"ThresholdOverlook\" : \"tNO\",\n\t\t\t\"SurchargeOverlook\" : \"tNO\",\n\t\t\t\"Remark1\" : null,\n\t\t\t\"ConCerti\" : null,\n\t\t\t\"DownPaymentInterimAccount\" : null,\n\t\t\t\"OperationCode347\" : \"ocSalesOrServicesRevenues\",\n\t\t\t\"InsuranceOperation347\" : \"tNO\",\n\t\t\t\"HierarchicalDeduction\" : \"tNO\",\n\t\t\t\"ShaamGroup\" : \"sgServicesAndAsset\",\n\t\t\t\"WithholdingTaxCertified\" : \"tNO\",\n\t\t\t\"BookkeepingCertified\" : \"tNO\",\n\t\t\t\"PlanningGroup\" : null,\n\t\t\t\"Affiliate\" : \"tNO\",\n\t\t\t\"Industry\" : null,\n\t\t\t\"VatIDNum\" : null,\n\t\t\t\"DatevAccount\" : null,\n\t\t\t\"DatevFirstDataEntry\" : \"tYES\",\n\t\t\t\"UseShippedGoodsAccount\" : \"tNO\",\n\t\t\t\"GTSRegNo\" : null,\n\t\t\t\"GTSBankAccountNo\" : null,\n\t\t\t\"GTSBillingAddrTel\" : null,\n\t\t\t\"ETaxWebSite\" : null,\n\t\t\t\"HouseBankIBAN\" : \"\",\n\t\t\t\"VATRegistrationNumber\" : null,\n\t\t\t\"RepresentativeName\" : null,\n\t\t\t\"IndustryType\" : null,\n\t\t\t\"BusinessType\" : null,\n\t\t\t\"Series\" : 942,\n\t\t\t\"AutomaticPosting\" : \"apNo\",\n\t\t\t\"InterestAccount\" : null,\n\t\t\t\"FeeAccount\" : null,\n\t\t\t\"CampaignNumber\" : null,\n\t\t\t\"AliasName\" : null,\n\t\t\t\"DefaultBlanketAgreementNumber\" : null,\n\t\t\t\"EffectiveDiscount\" : \"dgrLowestDiscount\",\n\t\t\t\"NoDiscounts\" : \"tNO\",\n\t\t\t\"EffectivePrice\" : \"epDefaultPriority\",\n\t\t\t\"EffectivePriceConsidersPriceBeforeDiscount\" : \"tNO\",\n\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\"EDISenderID\" : null,\n\t\t\t\"EDIRecipientID\" : null,\n\t\t\t\"ResidenNumber\" : \"rntSpanishFiscalID\",\n\t\t\t\"RelationshipCode\" : null,\n\t\t\t\"RelationshipDateFrom\" : null,\n\t\t\t\"RelationshipDateTill\" : null,\n\t\t\t\"UnifiedFederalTaxID\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"TypeOfOperation\" : null,\n\t\t\t\"EndorsableChecksFromBP\" : \"tYES\",\n\t\t\t\"AcceptsEndorsedChecks\" : \"tNO\",\n\t\t\t\"OwnerCode\" : null,\n\t\t\t\"BlockSendingMarketingContent\" : \"tNO\",\n\t\t\t\"AgentCode\" : null,\n\t\t\t\"PriceMode\" : null,\n\t\t\t\"EDocGenerationType\" : null,\n\t\t\t\"EDocStreet\" : null,\n\t\t\t\"EDocStreetNumber\" : null,\n\t\t\t\"EDocBuildingNumber\" : null,\n\t\t\t\"EDocZipCode\" : null,\n\t\t\t\"EDocCity\" : null,\n\t\t\t\"EDocCountry\" : null,\n\t\t\t\"EDocDistrict\" : null,\n\t\t\t\"EDocRepresentativeFirstName\" : null,\n\t\t\t\"EDocRepresentativeSurname\" : null,\n\t\t\t\"EDocRepresentativeCompany\" : null,\n\t\t\t\"EDocRepresentativeFiscalCode\" : null,\n\t\t\t\"EDocRepresentativeAdditionalId\" : null,\n\t\t\t\"EDocPECAddress\" : null,\n\t\t\t\"IPACodeForPA\" : null,\n\t\t\t\"UpdateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"21:20:52\",\n\t\t\t\"ExemptionMaxAmountValidationType\" : \"emaIndividual\",\n\t\t\t\"ECommerceMerchantID\" : null,\n\t\t\t\"UseBillToAddrToDetermineTax\" : \"tNO\",\n\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"18:32:43\",\n\t\t\t\"DefaultTransporterEntry\" : null,\n\t\t\t\"DefaultTransporterLineNumber\" : null,\n\t\t\t\"FCERelevant\" : \"tNO\",\n\t\t\t\"FCEValidateBaseDelivery\" : \"tNO\",\n\t\t\t\"MainUsage\" : null,\n\t\t\t\"EBooksVATExemptionCause\" : null,\n\t\t\t\"LegalText\" : null,\n\t\t\t\"DataVersion\" : 4,\n\t\t\t\"ExchangeRateForIncomingPayment\" : \"tYES\",\n\t\t\t\"ExchangeRateForOutgoingPayment\" : \"tYES\",\n\t\t\t\"CertificateDetails\" : null,\n\t\t\t\"DefaultCurrency\" : null,\n\t\t\t\"EORINumber\" : null,\n\t\t\t\"FCEAsPaymentMeans\" : \"tNO\",\n\t\t\t\"NotRelevantForMonthlyInvoice\" : \"tNO\",\n\t\t\t\"U_BENF\" : null,\n\t\t\t\"U_CBRT\" : \"S\",\n\t\t\t\"U_CO_RENT\" : null,\n\t\t\t\"U_CRID\" : \"IDENTIFICACION_TRIBUTARIA\",\n\t\t\t\"U_CRSI\" : \"contribuyente\",\n\t\t\t\"U_MDP\" : \"6\",\n\t\t\t\"U_NIDEN\" : null,\n\t\t\t\"U_PCFV\" : null,\n\t\t\t\"U_PRI\" : null,\n\t\t\t\"U_REPL\" : null,\n\t\t\t\"U_RRIV\" : \"S\",\n\t\t\t\"U_TIPCONT\" : 1,\n\t\t\t\"U_EPY_TSNF\" : null,\n\t\t\t\"U_NCCO\" : null,\n\t\t\t\"U_NCON\" : null,\n\t\t\t\"U_OTRO\" : null,\n\t\t\t\"U_TCON\" : \"1\",\n\t\t\t\"U_EXX_FE_TipoOperacion\" : \"2\",\n\t\t\t\"U_TipoTrato\" : \"N\",\n\t\t\t\"U_Especialidad\" : null,\n\t\t\t\"U_Cargo\" : null,\n\t\t\t\"U_Proyecto\" : null,\n\t\t\t\"U_AntTope\" : 0.0,\n\t\t\t\"U_RUT\" : null,\n\t\t\t\"U_PrcRet\" : null,\n\t\t\t\"U_PrcAnt\" : null,\n\t\t\t\"ElectronicProtocols\" : [],\n\t\t\t\"BPAddresses\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"AddressName\" : \"de Facturacion\",\n\t\t\t\t\t\"Street\" : \"-\",\n\t\t\t\t\t\"Block\" : null,\n\t\t\t\t\t\"ZipCode\" : null,\n\t\t\t\t\t\"City\" : null,\n\t\t\t\t\t\"County\" : null,\n\t\t\t\t\t\"Country\" : \"PY\",\n\t\t\t\t\t\"State\" : null,\n\t\t\t\t\t\"FederalTaxID\" : null,\n\t\t\t\t\t\"TaxCode\" : null,\n\t\t\t\t\t\"BuildingFloorRoom\" : null,\n\t\t\t\t\t\"AddressType\" : \"bo_BillTo\",\n\t\t\t\t\t\"AddressName2\" : null,\n\t\t\t\t\t\"AddressName3\" : null,\n\t\t\t\t\t\"TypeOfAddress\" : null,\n\t\t\t\t\t\"StreetNo\" : \"0\",\n\t\t\t\t\t\"BPCode\" : \"CLIE000651\",\n\t\t\t\t\t\"RowNum\" : 0,\n\t\t\t\t\t\"GlobalLocationNumber\" : null,\n\t\t\t\t\t\"Nationality\" : null,\n\t\t\t\t\t\"TaxOffice\" : null,\n\t\t\t\t\t\"GSTIN\" : null,\n\t\t\t\t\t\"GstType\" : null,\n\t\t\t\t\t\"CreateDate\" : \"2025-01-01T00:00:00Z\",\n\t\t\t\t\t\"CreateTime\" : \"21:20:52\",\n\t\t\t\t\t\"MYFType\" : null,\n\t\t\t\t\t\"TaasEnabled\" : \"tYES\",\n\t\t\t\t\t\"U_EXX_FE_DEPT\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_DIST\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BALO\" : \"1\",\n\t\t\t\t\t\"U_EXX_FE_BARR\" : null\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"ContactEmployees\" : [],\n\t\t\t\"BPAccountReceivablePaybleCollection\" : [],\n\t\t\t\"BPPaymentMethods\" : [],\n\t\t\t\"BPWithholdingTaxCollection\" : [],\n\t\t\t\"BPPaymentDates\" : [],\n\t\t\t\"BPBranchAssignment\" : \n\t\t\t[\n\t\t\t\t{\n\t\t\t\t\t\"BPCode\" : \"CLIE000651\",\n\t\t\t\t\t\"BPLID\" : 1,\n\t\t\t\t\t\"DisabledForBP\" : \"tNO\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"BPBankAccounts\" : [],\n\t\t\t\"BPFiscalTaxIDCollection\" : [],\n\t\t\t\"DiscountGroups\" : [],\n\t\t\t\"BPIntrastatExtension\" : {},\n\t\t\t\"BPBlockSendingMarketingContents\" : [],\n\t\t\t\"BPCurrenciesCollection\" : []\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/BusinessPartners?%24skip=0&%24top=5", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 318, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 1.396471, "namelookup_time": 0.000529, "connect_time": 0.012855, "pretransfer_time": 0.029164, "size_upload": 0, "size_download": 54025, "speed_download": 38686, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 1.357191, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 61509, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 29073, "connect_time_us": 12855, "namelookup_time_us": 529, "pretransfer_time_us": 29164, "redirect_time_us": 0, "starttransfer_time_us": 1357191, "posttransfer_time_us": 29198, "total_time_us": 1396471, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_68635304c27bc", "request_time": 1.3966038227081299, "cached": false}, "expires": 1751340083, "created": 1751339783}