{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#ItemProperties", "value": [{"Number": 1, "PropertyName": "ARTICULO DE BASCULA"}, {"Number": 2, "PropertyName": "MN"}, {"Number": 3, "PropertyName": "EX"}, {"Number": 4, "PropertyName": "CM"}, {"Number": 5, "PropertyName": "CP"}, {"Number": 6, "PropertyName": "Artí<PERSON><PERSON> propied<PERSON> 6"}, {"Number": 7, "PropertyName": "Artículos propied<PERSON> 7"}, {"Number": 8, "PropertyName": "Artí<PERSON>los propied<PERSON> 8"}, {"Number": 9, "PropertyName": "Artí<PERSON>los propied<PERSON> 9"}, {"Number": 10, "PropertyName": "Artí<PERSON>los propiedad 10"}, {"Number": 11, "PropertyName": "Artí<PERSON><PERSON> propied<PERSON> 11"}, {"Number": 12, "PropertyName": "Artí<PERSON><PERSON> propied<PERSON> 12"}, {"Number": 13, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 13"}, {"Number": 14, "PropertyName": "<PERSON>í<PERSON><PERSON> propied<PERSON> 14"}, {"Number": 15, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 15"}, {"Number": 16, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 16"}, {"Number": 17, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 17"}, {"Number": 18, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 18"}, {"Number": 19, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propied<PERSON> 19"}, {"Number": 20, "PropertyName": "<PERSON><PERSON><PERSON><PERSON> propiedad 20"}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#ItemProperties\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"Number\" : 1,\n\t\t\t\"PropertyName\" : \"ARTICULO DE BASCULA\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 2,\n\t\t\t\"PropertyName\" : \"MN\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 3,\n\t\t\t\"PropertyName\" : \"EX\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 4,\n\t\t\t\"PropertyName\" : \"CM\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 5,\n\t\t\t\"PropertyName\" : \"CP\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 6,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 6\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 7,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 7\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 8,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 8\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 9,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 9\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 10,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 10\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 11,\n\t\t\t\"PropertyName\" : \"Artí<PERSON>los propiedad 11\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 12,\n\t\t\t\"PropertyName\" : \"Art<PERSON><PERSON>los propiedad 12\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 13,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 13\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 14,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 14\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 15,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 15\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 16,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 16\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 17,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 17\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 18,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 18\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 19,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 19\"\n\t\t},\n\t\t{\n\t\t\t\"Number\" : 20,\n\t\t\t\"PropertyName\" : \"Artículos propiedad 20\"\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/ItemProperties?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 317, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.078897, "namelookup_time": 0.001356, "connect_time": 0.020416, "pretransfer_time": 0.04646, "size_upload": 0, "size_download": 1462, "speed_download": 18530, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.076854, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 49952, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 46334, "connect_time_us": 20416, "namelookup_time_us": 1356, "pretransfer_time_us": 46460, "redirect_time_us": 0, "starttransfer_time_us": 76854, "posttransfer_time_us": 46530, "total_time_us": 78897, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_6863560013f6c", "request_time": 0.07915282249450684, "cached": false}, "expires": 1751340845, "created": 1751340545}