{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#AssetTransfer", "value": []}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#AssetTransfer\",\n\t\"value\" : []\n}", "info": {"url": "https://*************:50000/b1s/v2/AssetTransfer?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 316, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.061082, "namelookup_time": 0.00067, "connect_time": 0.0154, "pretransfer_time": 0.036466, "size_upload": 0, "size_download": 99, "speed_download": 1620, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.061019, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 63108, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 36338, "connect_time_us": 15400, "namelookup_time_us": 670, "pretransfer_time_us": 36466, "redirect_time_us": 0, "starttransfer_time_us": 61019, "posttransfer_time_us": 36532, "total_time_us": 61082, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_686353faa6ed3", "request_time": 0.06128501892089844, "cached": false}, "expires": 1751340328, "created": 1751340028}