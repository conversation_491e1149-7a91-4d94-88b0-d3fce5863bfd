{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#Banks", "value": [{"BankCode": "002", "BankName": "BNF Banco Nacional de Fomento", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 28, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "003", "BankName": "BNA Banco de la NaciÃ²n Argentina", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 49, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "004", "BankName": "GNB Banco GNB Paraguay", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 50, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "005", "BankName": "Banco Do Brasil S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 51, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "006", "BankName": "Banco Citibank N.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 52, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "007", "BankName": "Banco BBVA Paraguay", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 53, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "008", "BankName": "Banco Sudameris Bank S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 54, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "017", "BankName": "Banco Itau Paraguay S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 55, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "020", "BankName": "Banco Continental S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 56, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "028", "BankName": "Banco Regional S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 57, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "030", "BankName": "Banco BASA S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 58, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "039", "BankName": "VisiÃ³n Banco S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 59, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "040", "BankName": "Banco Itapua S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 60, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "041", "BankName": "Banco Familiar S.A.E.C.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 61, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "042", "BankName": "Banco Atlas S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 62, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "043", "BankName": "Bancop S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 63, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "044", "BankName": "Interfisa Banco", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 64, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "2080", "BankName": "FIN. FIC S.A. DE FINANZAS", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 65, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}, {"BankCode": "2081", "BankName": "FIN. SOLAR S.A.", "AccountforOutgoingChecks": null, "BranchforOutgoingChecks": null, "NextCheckNumber": null, "SwiftNo": null, "IBAN": null, "CountryCode": "PY", "PostOffice": "tNO", "AbsoluteEntry": 66, "DefaultBankAccountKey": null, "DigitalPayments": "tNO"}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#Banks\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"BankCode\" : \"002\",\n\t\t\t\"BankName\" : \"BNF Banco Nacional de Fomento\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 28,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"003\",\n\t\t\t\"BankName\" : \"BNA Banco de la NaciÃ²n Argentina\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 49,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"004\",\n\t\t\t\"BankName\" : \"GNB Banco GNB Paraguay\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 50,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"005\",\n\t\t\t\"BankName\" : \"Banco Do Brasil S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 51,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"006\",\n\t\t\t\"BankName\" : \"Banco Citibank N.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 52,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"007\",\n\t\t\t\"BankName\" : \"Banco BBVA Paraguay\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 53,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"008\",\n\t\t\t\"BankName\" : \"Banco Sudameris Bank S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 54,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"017\",\n\t\t\t\"BankName\" : \"Banco Itau Paraguay S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 55,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"020\",\n\t\t\t\"BankName\" : \"Banco Continental S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 56,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"028\",\n\t\t\t\"BankName\" : \"Banco Regional S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 57,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"030\",\n\t\t\t\"BankName\" : \"Banco BASA S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 58,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"039\",\n\t\t\t\"BankName\" : \"VisiÃ³n Banco S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 59,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"040\",\n\t\t\t\"BankName\" : \"Banco Itapua S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 60,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"041\",\n\t\t\t\"BankName\" : \"Banco Familiar S.A.E.C.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 61,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"042\",\n\t\t\t\"BankName\" : \"Banco Atlas S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 62,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"043\",\n\t\t\t\"BankName\" : \"Bancop S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 63,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"044\",\n\t\t\t\"BankName\" : \"Interfisa Banco\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 64,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"2080\",\n\t\t\t\"BankName\" : \"FIN. FIC S.A. DE FINANZAS\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 65,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t},\n\t\t{\n\t\t\t\"BankCode\" : \"2081\",\n\t\t\t\"BankName\" : \"FIN. SOLAR S.A.\",\n\t\t\t\"AccountforOutgoingChecks\" : null,\n\t\t\t\"BranchforOutgoingChecks\" : null,\n\t\t\t\"NextCheckNumber\" : null,\n\t\t\t\"SwiftNo\" : null,\n\t\t\t\"IBAN\" : null,\n\t\t\t\"CountryCode\" : \"PY\",\n\t\t\t\"PostOffice\" : \"tNO\",\n\t\t\t\"AbsoluteEntry\" : 66,\n\t\t\t\"DefaultBankAccountKey\" : null,\n\t\t\t\"DigitalPayments\" : \"tNO\"\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/Banks?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 308, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 1.374607, "namelookup_time": 0.000884, "connect_time": 0.018592, "pretransfer_time": 0.041675, "size_upload": 0, "size_download": 6865, "speed_download": 4994, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 1.344107, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 62837, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 41564, "connect_time_us": 18592, "namelookup_time_us": 884, "pretransfer_time_us": 41675, "redirect_time_us": 0, "starttransfer_time_us": 1344107, "posttransfer_time_us": 41726, "total_time_us": 1374607, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_686353cd1ae91", "request_time": 1.3747870922088623, "cached": false}, "expires": 1751340284, "created": 1751339984}