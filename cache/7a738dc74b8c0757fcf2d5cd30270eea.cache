{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#EmployeesInfo", "value": [{"EmployeeID": 3, "LastName": "<PERSON><PERSON><PERSON><PERSON>", "FirstName": "<PERSON><PERSON>", "MiddleName": "<PERSON>", "Gender": "gt_Male", "JobTitle": "Operador", "EmployeeType": null, "Department": null, "Branch": null, "WorkStreet": null, "WorkBlock": null, "WorkZipCode": null, "WorkCity": null, "WorkCounty": null, "WorkCountryCode": null, "WorkStateCode": null, "Manager": null, "ApplicationUserID": null, "SalesPersonCode": null, "OfficePhone": null, "OfficeExtension": null, "MobilePhone": null, "Pager": null, "HomePhone": null, "Fax": null, "eMail": null, "StartDate": null, "StatusCode": null, "Salary": 0, "SalaryUnit": "scu_Month", "EmployeeCosts": 0, "EmployeeCostUnit": "scu_Month", "TerminationDate": null, "TreminationReason": null, "BankCode": "-1", "BankBranch": null, "BankBranchNum": null, "BankAccount": null, "HomeStreet": null, "HomeBlock": null, "HomeZipCode": null, "HomeCity": null, "HomeCounty": null, "HomeCountry": null, "HomeState": null, "DateOfBirth": null, "CountryOfBirth": null, "MartialStatus": "mts_Single", "NumOfChildren": null, "IdNumber": null, "CitizenshipCountryCode": null, "PassportNumber": null, "PassportExpirationDate": null, "Picture": null, "Remarks": null, "SalaryCurrency": "", "EmployeeCostsCurrency": "", "WorkBuildingFloorRoom": null, "HomeBuildingFloorRoom": null, "Position": null, "AttachmentEntry": null, "CostCenterCode": null, "CompanyNumber": null, "VacationPreviousYear": null, "VacationCurrentYear": null, "MunicipalityKey": null, "TaxClass": "0", "IncomeTaxLiability": "0", "Religion": "0", "PartnerReligion": "0", "ExemptionAmount": 0, "ExemptionUnit": "eeu_None", "ExemptionCurrency": "", "AdditionalAmount": 0, "AdditionalUnit": "eeu_None", "AdditionalCurrency": "", "TaxOfficeName": null, "TaxOfficeNumber": null, "HealthInsuranceName": null, "HealthInsuranceCode": null, "HealthInsuranceType": "", "SocialInsuranceNumber": null, "ProfessionStatus": "-1", "EducationStatus": "0", "PersonGroup": "-1", "JobTitleCode": null, "BankCodeForDATEV": null, "DeviatingBankAccountOwner": "tNO", "SpouseFirstName": null, "SpouseSurname": null, "ExternalEmployeeNumber": null, "BirthPlace": null, "PaymentMethod": "epm_BankTransfer", "STDCode": null, "CPF": null, "CRCNumber": null, "AccountantResponsible": "tNO", "LegalRepresentative": "tNO", "DIRFResponsible": "tNO", "CRCState": null, "Active": "tYES", "IDType": null, "BPLID": null, "PassportIssueDate": null, "PassportIssuer": null, "QualificationCode": "spedNA", "PRWebAccess": "tNO", "PreviousPRWebAccess": "tNO", "WorkStreetNumber": null, "HomeStreetNumber": null, "LinkedVendor": null, "CreateDate": "2025-04-08T00:00:00Z", "CreateTime": "14:00:39", "UpdateDate": "2025-04-08T00:00:00Z", "UpdateTime": "14:00:39", "EmployeeCode": "2", "ARetSEFAZ": "tNO", "GenderEx": "M", "U_CRID": "", "EmployeeAbsenceInfoLines": [], "EmployeeEducationInfoLines": [], "EmployeeReviewsInfoLines": [], "EmployeePreviousEmpoymentInfoLines": [], "EmployeeRolesInfoLines": [], "EmployeeSavingsPaymentInfoLines": [], "EmployeeBranchAssignment": []}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#EmployeesInfo\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"EmployeeID\" : 3,\n\t\t\t\"LastName\" : \"<PERSON><PERSON><PERSON><PERSON>\",\n\t\t\t\"FirstName\" : \"<PERSON><PERSON>\",\n\t\t\t\"<PERSON>Name\" : \"<PERSON>\",\n\t\t\t\"Gender\" : \"gt_Male\",\n\t\t\t\"JobTitle\" : \"Operador\",\n\t\t\t\"EmployeeType\" : null,\n\t\t\t\"Department\" : null,\n\t\t\t\"Branch\" : null,\n\t\t\t\"WorkStreet\" : null,\n\t\t\t\"WorkBlock\" : null,\n\t\t\t\"WorkZipCode\" : null,\n\t\t\t\"WorkCity\" : null,\n\t\t\t\"WorkCounty\" : null,\n\t\t\t\"WorkCountryCode\" : null,\n\t\t\t\"WorkStateCode\" : null,\n\t\t\t\"Manager\" : null,\n\t\t\t\"ApplicationUserID\" : null,\n\t\t\t\"SalesPersonCode\" : null,\n\t\t\t\"OfficePhone\" : null,\n\t\t\t\"OfficeExtension\" : null,\n\t\t\t\"MobilePhone\" : null,\n\t\t\t\"Pager\" : null,\n\t\t\t\"HomePhone\" : null,\n\t\t\t\"Fax\" : null,\n\t\t\t\"eMail\" : null,\n\t\t\t\"StartDate\" : null,\n\t\t\t\"StatusCode\" : null,\n\t\t\t\"Salary\" : 0.0,\n\t\t\t\"SalaryUnit\" : \"scu_Month\",\n\t\t\t\"EmployeeCosts\" : 0.0,\n\t\t\t\"EmployeeCostUnit\" : \"scu_Month\",\n\t\t\t\"TerminationDate\" : null,\n\t\t\t\"TreminationReason\" : null,\n\t\t\t\"BankCode\" : \"-1\",\n\t\t\t\"BankBranch\" : null,\n\t\t\t\"BankBranchNum\" : null,\n\t\t\t\"BankAccount\" : null,\n\t\t\t\"HomeStreet\" : null,\n\t\t\t\"HomeBlock\" : null,\n\t\t\t\"HomeZipCode\" : null,\n\t\t\t\"HomeCity\" : null,\n\t\t\t\"HomeCounty\" : null,\n\t\t\t\"HomeCountry\" : null,\n\t\t\t\"HomeState\" : null,\n\t\t\t\"DateOfBirth\" : null,\n\t\t\t\"CountryOfBirth\" : null,\n\t\t\t\"MartialStatus\" : \"mts_Single\",\n\t\t\t\"NumOfChildren\" : null,\n\t\t\t\"IdNumber\" : null,\n\t\t\t\"CitizenshipCountryCode\" : null,\n\t\t\t\"PassportNumber\" : null,\n\t\t\t\"PassportExpirationDate\" : null,\n\t\t\t\"Picture\" : null,\n\t\t\t\"Remarks\" : null,\n\t\t\t\"SalaryCurrency\" : \"\",\n\t\t\t\"EmployeeCostsCurrency\" : \"\",\n\t\t\t\"WorkBuildingFloorRoom\" : null,\n\t\t\t\"HomeBuildingFloorRoom\" : null,\n\t\t\t\"Position\" : null,\n\t\t\t\"AttachmentEntry\" : null,\n\t\t\t\"CostCenterCode\" : null,\n\t\t\t\"CompanyNumber\" : null,\n\t\t\t\"VacationPreviousYear\" : null,\n\t\t\t\"VacationCurrentYear\" : null,\n\t\t\t\"MunicipalityKey\" : null,\n\t\t\t\"TaxClass\" : \"0\",\n\t\t\t\"IncomeTaxLiability\" : \"0\",\n\t\t\t\"Religion\" : \"0\",\n\t\t\t\"PartnerReligion\" : \"0\",\n\t\t\t\"ExemptionAmount\" : 0.0,\n\t\t\t\"ExemptionUnit\" : \"eeu_None\",\n\t\t\t\"ExemptionCurrency\" : \"\",\n\t\t\t\"AdditionalAmount\" : 0.0,\n\t\t\t\"AdditionalUnit\" : \"eeu_None\",\n\t\t\t\"AdditionalCurrency\" : \"\",\n\t\t\t\"TaxOfficeName\" : null,\n\t\t\t\"TaxOfficeNumber\" : null,\n\t\t\t\"HealthInsuranceName\" : null,\n\t\t\t\"HealthInsuranceCode\" : null,\n\t\t\t\"HealthInsuranceType\" : \"\",\n\t\t\t\"SocialInsuranceNumber\" : null,\n\t\t\t\"ProfessionStatus\" : \"-1\",\n\t\t\t\"EducationStatus\" : \"0\",\n\t\t\t\"PersonGroup\" : \"-1\",\n\t\t\t\"JobTitleCode\" : null,\n\t\t\t\"BankCodeForDATEV\" : null,\n\t\t\t\"DeviatingBankAccountOwner\" : \"tNO\",\n\t\t\t\"SpouseFirstName\" : null,\n\t\t\t\"SpouseSurname\" : null,\n\t\t\t\"ExternalEmployeeNumber\" : null,\n\t\t\t\"BirthPlace\" : null,\n\t\t\t\"PaymentMethod\" : \"epm_BankTransfer\",\n\t\t\t\"STDCode\" : null,\n\t\t\t\"CPF\" : null,\n\t\t\t\"CRCNumber\" : null,\n\t\t\t\"AccountantResponsible\" : \"tNO\",\n\t\t\t\"LegalRepresentative\" : \"tNO\",\n\t\t\t\"DIRFResponsible\" : \"tNO\",\n\t\t\t\"CRCState\" : null,\n\t\t\t\"Active\" : \"tYES\",\n\t\t\t\"IDType\" : null,\n\t\t\t\"BPLID\" : null,\n\t\t\t\"PassportIssueDate\" : null,\n\t\t\t\"PassportIssuer\" : null,\n\t\t\t\"QualificationCode\" : \"spedNA\",\n\t\t\t\"PRWebAccess\" : \"tNO\",\n\t\t\t\"PreviousPRWebAccess\" : \"tNO\",\n\t\t\t\"WorkStreetNumber\" : null,\n\t\t\t\"HomeStreetNumber\" : null,\n\t\t\t\"LinkedVendor\" : null,\n\t\t\t\"CreateDate\" : \"2025-04-08T00:00:00Z\",\n\t\t\t\"CreateTime\" : \"14:00:39\",\n\t\t\t\"UpdateDate\" : \"2025-04-08T00:00:00Z\",\n\t\t\t\"UpdateTime\" : \"14:00:39\",\n\t\t\t\"EmployeeCode\" : \"2\",\n\t\t\t\"ARetSEFAZ\" : \"tNO\",\n\t\t\t\"GenderEx\" : \"M\",\n\t\t\t\"U_CRID\" : \"\",\n\t\t\t\"EmployeeAbsenceInfoLines\" : [],\n\t\t\t\"EmployeeEducationInfoLines\" : [],\n\t\t\t\"EmployeeReviewsInfoLines\" : [],\n\t\t\t\"EmployeePreviousEmpoymentInfoLines\" : [],\n\t\t\t\"EmployeeRolesInfoLines\" : [],\n\t\t\t\"EmployeeSavingsPaymentInfoLines\" : [],\n\t\t\t\"EmployeeBranchAssignment\" : []\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/EmployeesInfo?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 316, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 1.608649, "namelookup_time": 0.000851, "connect_time": 0.015749, "pretransfer_time": 0.034209, "size_upload": 0, "size_download": 3637, "speed_download": 2260, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 1.608551, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 50207, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 34070, "connect_time_us": 15749, "namelookup_time_us": 851, "pretransfer_time_us": 34209, "redirect_time_us": 0, "starttransfer_time_us": 1608551, "posttransfer_time_us": 34273, "total_time_us": 1608649, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_686356264f82f", "request_time": 1.6088690757751465, "cached": false}, "expires": 1751340885, "created": 1751340585}