{"data": {"success": true, "http_code": 200, "data": {"@odata.context": "https://*************:50000/b1s/v2/$metadata#Currencies", "value": [{"Code": "USD", "Name": "Dolar americano", "DocumentsCode": "USD", "InternationalDescription": "US Dollar", "HundredthName": "Centimos", "EnglishName": "US dollar", "EnglishHundredthName": "Cent", "PluralInternationalDescription": null, "PluralHundredthName": null, "PluralEnglishName": null, "PluralEnglishHundredthName": null, "Decimals": "cd2Digits", "Rounding": "rsNoRounding", "RoundingInPayment": "tNO", "MaxIncomingAmtDiff": 0, "MaxOutgoingAmtDiff": 0, "MaxIncomingAmtDiffPercent": 0, "MaxOutgoingAmtDiffPercent": 0}, {"Code": "EUR", "Name": "Euro", "DocumentsCode": "EUR", "InternationalDescription": "Euro", "HundredthName": "Centimos", "EnglishName": "Euro", "EnglishHundredthName": "Cent", "PluralInternationalDescription": null, "PluralHundredthName": null, "PluralEnglishName": null, "PluralEnglishHundredthName": null, "Decimals": "cd2Digits", "Rounding": "rsNoRounding", "RoundingInPayment": "tNO", "MaxIncomingAmtDiff": 0, "MaxOutgoingAmtDiff": 0, "MaxIncomingAmtDiffPercent": 0, "MaxOutgoingAmtDiffPercent": 0}, {"Code": "GS", "Name": "Guaranies", "DocumentsCode": "PYG", "InternationalDescription": "Guarani", "HundredthName": null, "EnglishName": "Guarani", "EnglishHundredthName": null, "PluralInternationalDescription": null, "PluralHundredthName": null, "PluralEnglishName": null, "PluralEnglishHundredthName": null, "Decimals": "cdWithoutDecimals", "Rounding": "rsNoRounding", "RoundingInPayment": "tNO", "MaxIncomingAmtDiff": 0, "MaxOutgoingAmtDiff": 0, "MaxIncomingAmtDiffPercent": 0, "MaxOutgoingAmtDiffPercent": 0}]}, "raw_response": "{\n\t\"@odata.context\" : \"https://*************:50000/b1s/v2/$metadata#Currencies\",\n\t\"value\" : \n\t[\n\t\t{\n\t\t\t\"Code\" : \"USD\",\n\t\t\t\"Name\" : \"Dolar americano\",\n\t\t\t\"DocumentsCode\" : \"USD\",\n\t\t\t\"InternationalDescription\" : \"US Dollar\",\n\t\t\t\"HundredthName\" : \"Centimos\",\n\t\t\t\"EnglishName\" : \"US dollar\",\n\t\t\t\"EnglishHundredthName\" : \"Cent\",\n\t\t\t\"PluralInternationalDescription\" : null,\n\t\t\t\"PluralHundredthName\" : null,\n\t\t\t\"PluralEnglishName\" : null,\n\t\t\t\"PluralEnglishHundredthName\" : null,\n\t\t\t\"Decimals\" : \"cd2Digits\",\n\t\t\t\"Rounding\" : \"rsNoRounding\",\n\t\t\t\"RoundingInPayment\" : \"tNO\",\n\t\t\t\"MaxIncomingAmtDiff\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiff\" : 0.0,\n\t\t\t\"MaxIncomingAmtDiffPercent\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiffPercent\" : 0.0\n\t\t},\n\t\t{\n\t\t\t\"Code\" : \"EUR\",\n\t\t\t\"Name\" : \"Euro\",\n\t\t\t\"DocumentsCode\" : \"EUR\",\n\t\t\t\"InternationalDescription\" : \"Euro\",\n\t\t\t\"HundredthName\" : \"Centimos\",\n\t\t\t\"EnglishName\" : \"Euro\",\n\t\t\t\"EnglishHundredthName\" : \"Cent\",\n\t\t\t\"PluralInternationalDescription\" : null,\n\t\t\t\"PluralHundredthName\" : null,\n\t\t\t\"PluralEnglishName\" : null,\n\t\t\t\"PluralEnglishHundredthName\" : null,\n\t\t\t\"Decimals\" : \"cd2Digits\",\n\t\t\t\"Rounding\" : \"rsNoRounding\",\n\t\t\t\"RoundingInPayment\" : \"tNO\",\n\t\t\t\"MaxIncomingAmtDiff\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiff\" : 0.0,\n\t\t\t\"MaxIncomingAmtDiffPercent\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiffPercent\" : 0.0\n\t\t},\n\t\t{\n\t\t\t\"Code\" : \"GS\",\n\t\t\t\"Name\" : \"Guaranies\",\n\t\t\t\"DocumentsCode\" : \"PYG\",\n\t\t\t\"InternationalDescription\" : \"Guarani\",\n\t\t\t\"HundredthName\" : null,\n\t\t\t\"EnglishName\" : \"Guarani\",\n\t\t\t\"EnglishHundredthName\" : null,\n\t\t\t\"PluralInternationalDescription\" : null,\n\t\t\t\"PluralHundredthName\" : null,\n\t\t\t\"PluralEnglishName\" : null,\n\t\t\t\"PluralEnglishHundredthName\" : null,\n\t\t\t\"Decimals\" : \"cdWithoutDecimals\",\n\t\t\t\"Rounding\" : \"rsNoRounding\",\n\t\t\t\"RoundingInPayment\" : \"tNO\",\n\t\t\t\"MaxIncomingAmtDiff\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiff\" : 0.0,\n\t\t\t\"MaxIncomingAmtDiffPercent\" : 0.0,\n\t\t\t\"MaxOutgoingAmtDiffPercent\" : 0.0\n\t\t}\n\t]\n}", "info": {"url": "https://*************:50000/b1s/v2/Currencies?%24skip=0&%24top=20", "content_type": "application/json;odata.metadata=minimal;charset=utf-8", "http_code": 200, "header_size": 299, "request_size": 312, "filetime": -1, "ssl_verify_result": 18, "redirect_count": 0, "total_time": 0.060571, "namelookup_time": 0.000532, "connect_time": 0.014556, "pretransfer_time": 0.031202, "size_upload": 0, "size_download": 1900, "speed_download": 31368, "speed_upload": 0, "download_content_length": -1, "upload_content_length": 0, "starttransfer_time": 0.060507, "redirect_time": 0, "redirect_url": "", "primary_ip": "*************", "certinfo": [], "primary_port": 50000, "local_ip": "**************", "local_port": 61717, "http_version": 2, "protocol": 2, "ssl_verifyresult": 0, "scheme": "https", "appconnect_time_us": 31111, "connect_time_us": 14556, "namelookup_time_us": 532, "pretransfer_time_us": 31202, "redirect_time_us": 0, "starttransfer_time_us": 60507, "posttransfer_time_us": 31244, "total_time_us": 60571, "effective_method": "GET", "capath": "", "cainfo": ""}, "request_id": "req_68635323c8765", "request_time": 0.060729026794433594, "cached": false}, "expires": 1751340113, "created": 1751339813}