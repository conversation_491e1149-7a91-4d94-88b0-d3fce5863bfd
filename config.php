<?php

/**
 * SAP Business One Configuration Helper
 * 
 * This helper loads configuration from environment variables or .env file
 * and returns it in the format expected by the SAPb1 library.
 */

/**
 * Load .env file if it exists
 */
function loadEnvFile($path = '.env') {
    if (!file_exists($path)) {
        return;
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        // Convert string booleans to actual booleans
        if (strtolower($value) === 'true') {
            $value = true;
        } elseif (strtolower($value) === 'false') {
            $value = false;
        }
        
        if (!array_key_exists($name, $_ENV)) {
            $_ENV[$name] = $value;
        }
    }
}

/**
 * Get SAP configuration from environment variables
 */
function getSapConfig() {
    // Load .env file
    loadEnvFile();
    
    $config = [
        'https' => $_ENV['SAP_HTTPS'] ?? true,
        'host' => $_ENV['SAP_HOST'] ?? 'localhost',
        'port' => (int)($_ENV['SAP_PORT'] ?? 50000),
        'username' => $_ENV['SAP_USERNAME'] ?? '',
        'password' => $_ENV['SAP_PASSWORD'] ?? '',
        'company' => $_ENV['SAP_COMPANY'] ?? '',
        'version' => (int)($_ENV['SAP_VERSION'] ?? 2),
    ];
    
    // Add SSL options if configured
    $sslOptions = [];
    if (isset($_ENV['SAP_SSL_VERIFY_PEER'])) {
        $sslOptions['verify_peer'] = $_ENV['SAP_SSL_VERIFY_PEER'];
    }
    if (isset($_ENV['SAP_SSL_VERIFY_PEER_NAME'])) {
        $sslOptions['verify_peer_name'] = $_ENV['SAP_SSL_VERIFY_PEER_NAME'];
    }
    if (isset($_ENV['SAP_SSL_CAFILE'])) {
        $sslOptions['cafile'] = $_ENV['SAP_SSL_CAFILE'];
    }
    if (isset($_ENV['SAP_SSL_ALLOW_SELF_SIGNED']) && $_ENV['SAP_SSL_ALLOW_SELF_SIGNED']) {
        $sslOptions['allow_self_signed'] = true;
    }
    
    if (!empty($sslOptions)) {
        $config['sslOptions'] = $sslOptions;
    }
    
    return $config;
}

/**
 * Example usage function
 */
function createSapClient() {
    // Since this library doesn't have proper autoloading, include files manually
    require_once 'SAPb1/Config.php';
    require_once 'SAPb1/Request.php';
    require_once 'SAPb1/Response.php';
    require_once 'SAPb1/SAPException.php';
    require_once 'SAPb1/SAPClient.php';
    require_once 'SAPb1/Service.php';
    require_once 'SAPb1/Query.php';
    
    // Include filter classes
    require_once 'SAPb1/Filters/Filter.php';
    require_once 'SAPb1/Filters/Equal.php';
    require_once 'SAPb1/Filters/Contains.php';
    require_once 'SAPb1/Filters/StartsWith.php';
    require_once 'SAPb1/Filters/EndsWith.php';
    require_once 'SAPb1/Filters/MoreThan.php';
    require_once 'SAPb1/Filters/LessThan.php';
    require_once 'SAPb1/Filters/Between.php';
    require_once 'SAPb1/Filters/InArray.php';
    require_once 'SAPb1/Filters/NotEqual.php';
    require_once 'SAPb1/Filters/NotInArray.php';
    require_once 'SAPb1/Filters/Raw.php';
    require_once 'SAPb1/Filters/MoreThanEqual.php';
    require_once 'SAPb1/Filters/LessThanEqual.php';
    
    $config = getSapConfig();
    
    try {
        $sap = SAPb1\SAPClient::new($config);
        return $sap;
    } catch (SAPb1\SAPException $e) {
        echo "SAP Connection Error: " . $e->getMessage() . "\n";
        return null;
    }
}
