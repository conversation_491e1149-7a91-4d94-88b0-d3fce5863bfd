/**
 * API Module
 * Handles all AJAX requests to the backend/SAP B1 Service Layer
 */
window.API = (function() {
    'use strict';

    // Configuration
    const config = {
        baseUrl: '/api',
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
    };

    /**
     * Make HTTP request
     */
    async function request(method, endpoint, data = null, options = {}) {
        const url = `${config.baseUrl}${endpoint}`;
        const sessionId = Auth.getCurrentSession();
        
        const requestOptions = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                ...options.headers
            },
            ...options
        };

        // Add session header if available
        if (sessionId) {
            requestOptions.headers['Authorization'] = `Bearer ${sessionId}`;
        }

        // Add request body for POST/PUT/PATCH
        if (data && ['POST', 'PUT', 'PATCH'].includes(requestOptions.method)) {
            requestOptions.body = JSON.stringify(data);
        }

        try {
            console.log(`API ${method.toUpperCase()} ${url}`, data);
            
            const response = await fetchWithTimeout(url, requestOptions, config.timeout);
            
            // Handle response
            if (response.ok) {
                const contentType = response.headers.get('content-type');
                let responseData;
                
                if (contentType && contentType.includes('application/json')) {
                    responseData = await response.json();
                } else {
                    responseData = await response.text();
                }
                
                console.log(`API Response:`, responseData);
                return {
                    success: true,
                    data: responseData,
                    status: response.status,
                    headers: response.headers
                };
            } else {
                // Handle HTTP errors
                let errorData;
                try {
                    errorData = await response.json();
                } catch (e) {
                    errorData = { message: response.statusText };
                }
                
                console.error(`API Error ${response.status}:`, errorData);
                
                // Handle authentication errors
                if (response.status === 401) {
                    handleAuthError();
                }
                
                return {
                    success: false,
                    error: errorData,
                    status: response.status,
                    message: errorData.message || `HTTP ${response.status}: ${response.statusText}`
                };
            }
        } catch (error) {
            console.error('API Request failed:', error);
            
            // Handle network errors
            if (error.name === 'AbortError') {
                            return {
                success: false,
                error: 'Tiempo de espera agotado',
                message: 'Tiempo de espera agotado. Por favor, inténtelo de nuevo.'
            };
            }
            
                    return {
            success: false,
            error: error.message,
            message: 'Error de red. Por favor, verifique su conexión.'
        };
        }
    }

    /**
     * Fetch with timeout
     */
    function fetchWithTimeout(url, options, timeout) {
        return Promise.race([
            fetch(url, options),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('AbortError')), timeout)
            )
        ]);
    }

    /**
     * GET request
     */
    async function get(endpoint, params = {}, options = {}) {
        let url = endpoint;
        
        // Add query parameters
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                    searchParams.append(key, value);
                }
            });
            url += `?${searchParams.toString()}`;
        }
        
        return request('GET', url, null, options);
    }

    /**
     * POST request
     */
    async function post(endpoint, data, options = {}) {
        return request('POST', endpoint, data, options);
    }

    /**
     * PUT request
     */
    async function put(endpoint, data, options = {}) {
        return request('PUT', endpoint, data, options);
    }

    /**
     * PATCH request
     */
    async function patch(endpoint, data, options = {}) {
        return request('PATCH', endpoint, data, options);
    }

    /**
     * DELETE request
     */
    async function del(endpoint, options = {}) {
        return request('DELETE', endpoint, null, options);
    }

    /**
     * Upload file
     */
    async function upload(endpoint, formData, options = {}) {
        const url = `${config.baseUrl}${endpoint}`;
        const sessionId = Auth.getCurrentSession();
        
        const requestOptions = {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                ...options.headers
            },
            ...options
        };

        // Don't set Content-Type for FormData - browser will set it with boundary
        delete requestOptions.headers['Content-Type'];

        // Add session header if available
        if (sessionId) {
            requestOptions.headers['Authorization'] = `Bearer ${sessionId}`;
        }

        try {
            console.log(`API UPLOAD ${url}`, formData);
            
            const response = await fetchWithTimeout(url, requestOptions, config.timeout);
            
            if (response.ok) {
                const responseData = await response.json();
                console.log(`Upload Response:`, responseData);
                return {
                    success: true,
                    data: responseData,
                    status: response.status
                };
            } else {
                const errorData = await response.json().catch(() => ({ message: response.statusText }));
                console.error(`Upload Error ${response.status}:`, errorData);
                return {
                    success: false,
                    error: errorData,
                    status: response.status,
                    message: errorData.message || `Error en la carga: ${response.statusText}`
                };
            }
        } catch (error) {
            console.error('Upload failed:', error);
                    return {
            success: false,
            error: error.message,
            message: 'Error en la carga. Por favor, inténtelo de nuevo.'
        };
        }
    }

    /**
     * Handle authentication errors
     */
    function handleAuthError() {
        console.warn('Error de autenticación - redirigiendo al inicio de sesión');
        
        // Clear session
        localStorage.removeItem('sap_user');
        localStorage.removeItem('sap_session');
        
        // Redirect to login
        if (window.App) {
            App.showMessage('Sesión expirada. Por favor, inicie sesión nuevamente.', 'error');
            // Could trigger re-login flow here
        }
    }

    /**
     * Retry failed requests
     */
    async function requestWithRetry(method, endpoint, data = null, options = {}) {
        let lastError;
        
        for (let attempt = 1; attempt <= config.retryAttempts; attempt++) {
            try {
                const result = await request(method, endpoint, data, options);
                
                if (result.success || result.status < 500) {
                    // Don't retry for client errors (4xx) or successful requests
                    return result;
                }
                
                lastError = result;
            } catch (error) {
                lastError = error;
            }
            
            if (attempt < config.retryAttempts) {
                console.log(`Solicitud falló, reintentando en ${config.retryDelay}ms... (intento ${attempt}/${config.retryAttempts})`);
                await new Promise(resolve => setTimeout(resolve, config.retryDelay));
            }
        }
        
        console.error(`Solicitud falló después de ${config.retryAttempts} intentos:`, lastError);
        return lastError;
    }

    /**
     * SAP B1 specific API calls
     */
    const sap = {
        // Business Partners
        async getBusinessPartners(filter = {}) {
            return get('/businesspartners', filter);
        },
        
        async getBusinessPartner(cardCode) {
            return get(`/businesspartners/${cardCode}`);
        },
        
        async createBusinessPartner(partnerData) {
            return post('/businesspartners', partnerData);
        },
        
        async updateBusinessPartner(cardCode, partnerData) {
            return put(`/businesspartners/${cardCode}`, partnerData);
        },
        
        // Items
        async getItems(filter = {}) {
            return get('/items', filter);
        },
        
        async getItem(itemCode) {
            return get(`/items/${itemCode}`);
        },
        
        async createItem(itemData) {
            return post('/items', itemData);
        },
        
        // Documents
        async getSalesOrders(filter = {}) {
            return get('/orders', filter);
        },
        
        async getSalesOrder(docEntry) {
            return get(`/orders/${docEntry}`);
        },
        
        async createSalesOrder(orderData) {
            return post('/orders', orderData);
        },
        
        async getPurchaseOrders(filter = {}) {
            return get('/purchaseorders', filter);
        },
        
        async createPurchaseOrder(orderData) {
            return post('/purchaseorders', orderData);
        },
        
        // Reports
        async getReport(reportName, parameters = {}) {
            return get(`/reports/${reportName}`, parameters);
        },
        
        // General Ledger
        async getGLAccounts(filter = {}) {
            return get('/glaccounts', filter);
        },
        
        // Inventory
        async getWarehouses() {
            return get('/warehouses');
        },
        
        async getStockLevels(itemCode = null) {
            const params = itemCode ? { itemCode } : {};
            return get('/stock', params);
        }
    };

    /**
     * Set configuration
     */
    function setConfig(newConfig) {
        Object.assign(config, newConfig);
    }

    /**
     * Get configuration
     */
    function getConfig() {
        return { ...config };
    }

    /**
     * Public API
     */
    return {
        request,
        get,
        post,
        put,
        patch,
        delete: del,
        upload,
        requestWithRetry,
        sap,
        setConfig,
        getConfig
    };
})();
