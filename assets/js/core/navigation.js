/**
 * <PERSON><PERSON><PERSON><PERSON> de Navegación
 * Maneja la navegación entre módulos y la gestión del estado de navegación
 */
window.Navigation = (function() {
    'use strict';

    // Estado de navegación
    let currentModule = null;
    let navigationHistory = [];

    /**
     * Inicializar el módulo de navegación
     */
    function init() {
        console.log('Inicializando módulo de navegación...');
        bindNavigationEvents();
    }

    /**
     * Vincular eventos de navegación
     */
    function bindNavigationEvents() {
        // Manejar clics en elementos de navegación
        document.addEventListener('click', function(e) {
            const navItem = e.target.closest('.nav-item');
            if (navItem) {
                const module = navItem.getAttribute('data-module');
                if (module) {
                    navigateToModule(module);
                }
            }
        });

        // Manejar navegación del navegador
        window.addEventListener('popstate', function(e) {
            if (e.state && e.state.module) {
                loadModule(e.state.module, false);
            }
        });
    }

    /**
     * Navegar a un módulo específico
     */
    function navigateToModule(moduleName) {
        console.log('Navegando al módulo:', moduleName);
        
        // Actualizar estado de navegación
        setActiveModule(moduleName);
        
        // Cargar contenido del módulo
        loadModuleContent(moduleName);
        
        // Actualizar historial del navegador
        const state = { module: moduleName };
        history.pushState(state, '', `#${moduleName}`);
        
        // Agregar al historial de navegación
        navigationHistory.push(moduleName);
    }

    /**
     * Establecer módulo activo
     */
    function setActiveModule(moduleName) {
        currentModule = moduleName;
        
        // Actualizar clase activa en la navegación
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeItem = document.querySelector(`[data-module="${moduleName}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    /**
     * Cargar contenido del módulo
     */
    function loadModuleContent(moduleName) {
        const contentArea = document.getElementById('content-area');
        if (!contentArea) return;

        // Mostrar indicador de carga
        contentArea.innerHTML = '<div class="loading">Cargando módulo...</div>';

        // Mapeo de módulos a títulos en español
        const moduleLabels = {
            'sales': 'Ventas - CxC',
            'purchasing': 'Compras - CxP',
            'inventory': 'Inventario',
            'partners': 'Socios de Negocio',
            'banking': 'Bancos',
            'financials': 'Finanzas',
            'reports': 'Informes',
            'administration': 'Administración'
        };

        // Simular carga del módulo
        setTimeout(() => {
            const moduleLabel = moduleLabels[moduleName] || moduleName;
            contentArea.innerHTML = `
                <div class="module-container">
                    <div class="module-header">
                        <h1>${moduleLabel}</h1>
                        <div class="module-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Nuevo
                            </button>
                            <button class="btn">
                                <i class="fas fa-search"></i>
                                Buscar
                            </button>
                        </div>
                    </div>
                    <div class="module-content">
                        <p>Contenido del módulo ${moduleLabel} se cargará aquí.</p>
                        <p>Esta es una implementación de demostración.</p>
                    </div>
                </div>
            `;
        }, 500);
    }

    /**
     * Obtener módulo actual
     */
    function getCurrentModule() {
        return currentModule;
    }

    /**
     * Volver al módulo anterior
     */
    function goBack() {
        if (navigationHistory.length > 1) {
            navigationHistory.pop(); // Remover módulo actual
            const previousModule = navigationHistory[navigationHistory.length - 1];
            navigateToModule(previousModule);
        }
    }

    /**
     * Obtener historial de navegación
     */
    function getNavigationHistory() {
        return [...navigationHistory];
    }

    // API pública
    return {
        init,
        navigateToModule,
        setActiveModule,
        getCurrentModule,
        goBack,
        getNavigationHistory
    };
})();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    Navigation.init();
}); 