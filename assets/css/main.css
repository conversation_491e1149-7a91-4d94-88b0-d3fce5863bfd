/* Main Application Layout */
.main-app {
    display: none;
    height: 100vh;
    flex-direction: column;
}

.main-app.active {
    display: flex;
}

/* Header */
.app-header {
    background: #2c3e50;
    color: white;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    width: 60px;
    height: 30px;
    background: #0078d4;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.header-logo::before {
    content: "SAP";
}

.company-name {
    font-size: 18px;
    font-weight: 500;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 20px;
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ccc;
    border-radius: 20px;
    font-size: 14px;
    background: white;
}

.search-box input:focus {
    outline: none;
    border-color: #0078d4;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.database-badge {
    background: #0078d4;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.database-badge.prod {
    background: #dc3545;
}

.logout-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 3px;
    font-size: 16px;
    transition: background-color 0.2s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Navigation */
.main-nav {
    background: #34495e;
    width: 250px;
    height: calc(100vh - 60px);
    overflow-y: auto;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #ecf0f1;
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item:hover {
    background: #3d566e;
    padding-left: 25px;
}

.nav-item.active {
    background: #0078d4;
    border-left: 4px solid #106ebe;
}

.nav-item i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
}

.nav-item span {
    font-size: 14px;
    font-weight: 500;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 20px;
    background: #f8f9fa;
    overflow-y: auto;
    margin-left: 250px;
}

/* Dashboard */
.dashboard h1 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 300;
}

.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.widget {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.widget h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 500;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px dashed #dee2e6;
    border-radius: 6px;
    background: #f8f9fa;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
    font-size: 13px;
}

.quick-btn:hover {
    border-color: #0078d4;
    background: #f0f8ff;
    color: #0078d4;
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 24px;
    margin-bottom: 8px;
}

/* Module Content */
.module-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.module-title {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 500;
}

.module-actions {
    display: flex;
    gap: 10px;
}

/* Toolbar */
.toolbar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.toolbar-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Status Bar */
.status-bar {
    background: #2c3e50;
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    margin-top: auto;
}

.status-left {
    display: flex;
    gap: 20px;
}

.status-right {
    display: flex;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        width: 100%;
        height: auto;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 1000;
        transform: translateY(100%);
        transition: transform 0.3s;
    }
    
    .main-nav.mobile-open {
        transform: translateY(0);
    }
    
    .content-area {
        margin-left: 0;
        padding-bottom: 80px;
    }
    
    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
    }
    
    .nav-item {
        min-width: 100px;
        flex-direction: column;
        padding: 10px;
        text-align: center;
    }
    
    .nav-item i {
        margin-right: 0;
        margin-bottom: 5px;
    }
    
    .nav-item span {
        font-size: 12px;
    }
    
    .header-center {
        display: none;
    }
    
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: 0 15px;
    }
    
    .content-area {
        padding: 15px;
    }
    
    .company-name {
        display: none;
    }
    
    .user-info {
        flex-direction: column;
        gap: 5px;
    }
}
