/* Login Container */
.login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.login-container.active {
    display: flex;
}

/* Login Form */
.login-form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 400px;
    margin: 20px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.sap-logo {
    width: 80px;
    height: 40px;
    margin-bottom: 15px;
    background: #0078d4;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
    margin: 0 auto 15px;
}

.sap-logo::before {
    content: "SAP";
}

.login-header h2 {
    color: #333;
    margin-bottom: 5px;
    font-size: 24px;
    font-weight: 300;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

/* Database Switch */
.database-switch {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.database-switch input[type="radio"] {
    display: none;
}

.db-option {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9f9f9;
    font-size: 13px;
}

.db-option i {
    display: block;
    font-size: 20px;
    margin-bottom: 8px;
}

.db-option.dev {
    color: #28a745;
}

.db-option.prod {
    color: #dc3545;
}

.database-switch input[type="radio"]:checked + .db-option {
    border-color: #0078d4;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 120, 212, 0.2);
}

.database-switch input[type="radio"]:checked + .db-option.dev {
    border-color: #28a745;
    background: #f8fff8;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
}

.database-switch input[type="radio"]:checked + .db-option.prod {
    border-color: #dc3545;
    background: #fff8f8;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.2);
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 12px;
    background: #0078d4;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-btn:hover {
    background: #106ebe;
}

.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Login Status */
.login-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    font-size: 14px;
    display: none;
}

.login-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.login-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.login-status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* Responsive */
@media (max-width: 480px) {
    .login-form {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .database-switch {
        flex-direction: column;
    }
    
    .db-option {
        padding: 12px;
    }
    
    .db-option i {
        font-size: 18px;
        margin-bottom: 6px;
    }
}
