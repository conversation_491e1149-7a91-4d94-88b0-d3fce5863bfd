# SAP Business One Configuration Setup

This document explains how to configure the SAP Business One Service Layer connection using environment variables.

## Quick Setup

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your SAP B1 details:**
   ```bash
   nano .env
   ```

3. **Update the configuration values:**
   ```env
   SAP_HTTPS=true
   SAP_HOST=your-sap-server-ip
   SAP_PORT=50000
   SAP_USERNAME=your-username
   SAP_PASSWORD=your-password
   SAP_COMPANY=your-company-db
   SAP_VERSION=2
   ```

## Configuration Options

### Required Settings

| Variable | Description | Example |
|----------|-------------|---------|
| `SAP_HOST` | SAP Server IP or hostname | `*************` |
| `SAP_USERNAME` | SAP B1 username | `manager` |
| `SAP_PASSWORD` | SAP B1 password | `your-password` |
| `SAP_COMPANY` | Company database name | `SBODEMOUS` |

### Optional Settings

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `SAP_HTTPS` | Use HTTPS connection | `true` | `true` or `false` |
| `SAP_PORT` | Service Layer port | `50000` | `50000` |
| `SAP_VERSION` | API version | `2` | `1` or `2` |
| `SAP_SSL_VERIFY_PEER` | Verify SSL peer | `true` | `true` or `false` |
| `SAP_SSL_VERIFY_PEER_NAME` | Verify SSL peer name | `true` | `true` or `false` |
| `SAP_SSL_CAFILE` | SSL certificate file path | - | `/path/to/cert.crt` |

## Usage

### Using the Configuration Helper

```php
<?php
require_once 'config.php';

// Create SAP client using environment configuration
$sap = createSapClient();

if ($sap) {
    echo "Connected to SAP Business One!";
    
    // Use the client...
    $orders = $sap->getService('Orders');
    $result = $orders->query()->limit(10)->find();
}
```

### Manual Configuration (Alternative)

If you prefer not to use environment variables, you can still configure manually:

```php
<?php
use SAPb1\SAPClient;

$config = [
    'https' => true,
    'host' => 'your-sap-server',
    'port' => 50000,
    'username' => 'your-username',
    'password' => 'your-password',
    'company' => 'your-company',
    'version' => 2,
    'sslOptions' => [
        'verify_peer' => true,
        'verify_peer_name' => true,
    ]
];

$sap = SAPClient::new($config);
```

## Running the Example

Once configured, you can run the example script:

```bash
php example.php
```

This will demonstrate:
- Connecting to SAP B1 Service Layer
- Querying Business Partners
- Querying Sales Orders
- Using filters

## Security Notes

- **Never commit your `.env` file to version control**
- Keep your SAP credentials secure
- Use appropriate SSL settings for production environments
- Consider using SSL certificates for secure connections

## Troubleshooting

### Common Issues

1. **Connection refused**: Check if the Service Layer is running and the port is correct
2. **SSL errors**: Try setting `SAP_SSL_VERIFY_PEER=false` for testing (not recommended for production)
3. **Authentication failed**: Verify username, password, and company database name
4. **Timeout errors**: Check network connectivity and firewall settings

### Enable Debug Mode

You can add error reporting to see detailed error messages:

```php
<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';
// ... rest of your code
```
