<?php

/**
 * Example usage of SAP Business One Service Layer API
 * 
 * This example shows how to:
 * 1. Load configuration from .env file
 * 2. Connect to SAP B1 Service Layer
 * 3. Perform basic operations
 */

require_once 'config.php';

// Load configuration and create SAP client
$sap = createSapClient();

if (!$sap) {
    echo "Failed to connect to SAP Business One Service Layer\n";
    exit(1);
}

echo "Successfully connected to SAP Business One!\n";

try {
    // Example 1: Get Business Partners
    echo "\n=== Example 1: Querying Business Partners ===\n";
    $businessPartners = $sap->getService('BusinessPartners');
    
    $result = $businessPartners->query()
        ->select('CardCode,CardName,CardType')
        ->limit(5)
        ->find();
    
    if ($result->getStatusCode() === 200) {
        $data = json_decode($result->getBody(), true);
        echo "Found " . count($data['value']) . " business partners:\n";
        foreach ($data['value'] as $bp) {
            echo "  - {$bp['CardCode']}: {$bp['CardName']}\n";
        }
    }
    
    // Example 2: Get Sales Orders
    echo "\n=== Example 2: Querying Sales Orders ===\n";
    $orders = $sap->getService('Orders');
    
    $result = $orders->query()
        ->select('DocEntry,DocNum,CardCode,DocTotal')
        ->orderBy('DocNum', 'desc')
        ->limit(5)
        ->find();
    
    if ($result->getStatusCode() === 200) {
        $data = json_decode($result->getBody(), true);
        echo "Found " . count($data['value']) . " sales orders:\n";
        foreach ($data['value'] as $order) {
            echo "  - Order #{$order['DocNum']}: {$order['CardCode']} - Total: {$order['DocTotal']}\n";
        }
    }
    
    // Example 3: Using filters
    echo "\n=== Example 3: Using Filters ===\n";
    $items = $sap->getService('Items');
    
    // Using the Equal filter
    $result = $items->query()
        ->select('ItemCode,ItemName,InvntryUom')
        ->where(new SAPb1\Filters\Equal('Mainsupplier', 'V001'))
        ->limit(3)
        ->find();
    
    if ($result->getStatusCode() === 200) {
        $data = json_decode($result->getBody(), true);
        echo "Found " . count($data['value']) . " items with supplier V001:\n";
        foreach ($data['value'] as $item) {
            echo "  - {$item['ItemCode']}: {$item['ItemName']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nExample completed!\n";
