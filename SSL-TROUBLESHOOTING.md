# SSL Troubleshooting Guide for Self-Signed Certificates

This guide helps you connect to SAP Business One Service Layer when using self-signed SSL certificates.

## ⚠️ Security Warning

**The settings below bypass SSL verification and should ONLY be used for development/testing environments. Never use these settings in production!**

## Quick Setup for Self-Signed Certificates

### Option 1: Environment Variables (Recommended)

1. **Copy the self-signed example:**
   ```bash
   cp .env.self-signed-example .env
   ```

2. **Edit your credentials:**
   ```bash
   nano .env
   ```

3. **Update with your SAP server details:**
   ```env
   SAP_HOST=your-sap-server-ip
   SAP_USERNAME=your-username
   SAP_PASSWORD=your-password
   SAP_COMPANY=your-company-db
   
   # SSL bypass for self-signed certificates
   SAP_SSL_VERIFY_PEER=false
   SAP_SSL_VERIFY_PEER_NAME=false
   SAP_SSL_ALLOW_SELF_SIGNED=true
   ```

### Option 2: Manual Configuration

```php
<?php
use SAPb1\SAPClient;

$config = [
    'https' => true,
    'host' => 'your-sap-server',
    'port' => 50000,
    'username' => 'your-username',
    'password' => 'your-password',
    'company' => 'your-company',
    'version' => 2,
    'sslOptions' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
        'allow_self_signed' => true,
    ]
];

$sap = SAPClient::new($config);
```

## SSL Configuration Options

| Option | Description | Self-Signed Value | Production Value |
|--------|-------------|-------------------|------------------|
| `verify_peer` | Verify the peer's SSL certificate | `false` | `true` |
| `verify_peer_name` | Verify peer name against certificate | `false` | `true` |
| `allow_self_signed` | Allow self-signed certificates | `true` | `false` |
| `cafile` | Path to certificate authority file | - | `/path/to/ca.crt` |

## Common SSL Errors and Solutions

### Error: "SSL certificate problem: self signed certificate"

**Solution:** Set these environment variables:
```env
SAP_SSL_VERIFY_PEER=false
SAP_SSL_VERIFY_PEER_NAME=false
SAP_SSL_ALLOW_SELF_SIGNED=true
```

### Error: "SSL certificate problem: unable to get local issuer certificate"

**Solution:** Either disable verification or provide a CA file:
```env
# Option 1: Disable verification (development only)
SAP_SSL_VERIFY_PEER=false

# Option 2: Provide CA certificate (recommended)
SAP_SSL_CAFILE=/path/to/your/ca-bundle.crt
```

### Error: "SSL certificate problem: certificate verify failed"

**Solution:** This usually means the hostname doesn't match the certificate:
```env
SAP_SSL_VERIFY_PEER_NAME=false
```

## Testing Your SSL Configuration

Create a test script to verify your SSL settings:

```php
<?php
require_once 'config.php';

echo "Testing SAP B1 SSL connection...\n";

$config = getSapConfig();
echo "Configuration loaded:\n";
echo "- Host: " . $config['host'] . "\n";
echo "- Port: " . $config['port'] . "\n";
echo "- HTTPS: " . ($config['https'] ? 'Yes' : 'No') . "\n";

if (isset($config['sslOptions'])) {
    echo "- SSL Options:\n";
    foreach ($config['sslOptions'] as $key => $value) {
        echo "  - $key: " . ($value === true ? 'true' : ($value === false ? 'false' : $value)) . "\n";
    }
}

try {
    $sap = createSapClient();
    if ($sap) {
        echo "\n✅ Successfully connected to SAP Business One Service Layer!\n";
    } else {
        echo "\n❌ Failed to connect to SAP Business One Service Layer\n";
    }
} catch (Exception $e) {
    echo "\n❌ Connection error: " . $e->getMessage() . "\n";
}
```

## Production Recommendations

For production environments with self-signed certificates, consider these alternatives:

1. **Add the certificate to your CA bundle:**
   ```env
   SAP_SSL_CAFILE=/path/to/your-custom-ca-bundle.crt
   SAP_SSL_VERIFY_PEER=true
   SAP_SSL_VERIFY_PEER_NAME=true
   ```

2. **Use a proper SSL certificate from a trusted CA**

3. **Set up a reverse proxy with proper SSL termination**

## Advanced SSL Options

The library supports all PHP stream context SSL options:

```php
'sslOptions' => [
    'verify_peer' => false,
    'verify_peer_name' => false,
    'allow_self_signed' => true,
    'verify_depth' => 0,
    'ciphers' => 'HIGH:!SSLv2:!SSLv3',
    'capture_peer_cert' => true,
    'SNI_enabled' => true,
]
```

## Debugging SSL Issues

Enable verbose error reporting:

```php
<?php
// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Enable SSL debug (be careful with sensitive data)
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
        'capture_peer_cert' => true,
        'capture_peer_cert_chain' => true,
    ]
]);

// Your SAP connection code here...
```

Remember: Always prioritize security in production environments!
