<?php

/**
 * SSL Connection Test for SAP Business One Service Layer
 * 
 * This script helps test SSL connections and troubleshoot certificate issues.
 */

require_once 'config.php';

echo "=== SAP Business One SSL Connection Test ===\n\n";

// Load configuration
$config = getSapConfig();

echo "Configuration Details:\n";
echo "- Host: " . $config['host'] . "\n";
echo "- Port: " . $config['port'] . "\n";
echo "- HTTPS: " . ($config['https'] ? 'Yes' : 'No') . "\n";
echo "- Version: " . $config['version'] . "\n";

if (isset($config['sslOptions']) && !empty($config['sslOptions'])) {
    echo "\nSSL Options:\n";
    foreach ($config['sslOptions'] as $key => $value) {
        $displayValue = $value;
        if (is_bool($value)) {
            $displayValue = $value ? 'true' : 'false';
        }
        echo "- $key: $displayValue\n";
    }
} else {
    echo "\nSSL Options: Using PHP defaults (strict verification)\n";
}

echo "\n" . str_repeat("-", 50) . "\n";

// Test basic connectivity first
echo "Testing basic connectivity...\n";

$scheme = $config['https'] ? 'https' : 'http';
$testUrl = $scheme . '://' . $config['host'] . ':' . $config['port'] . '/b1s/v' . $config['version'] . '/';

echo "Testing URL: $testUrl\n";

// Test with current SSL settings
echo "\nTesting with current SSL settings...\n";

try {
    $sap = createSapClient();
    if ($sap) {
        echo "✅ SUCCESS: Connected to SAP Business One Service Layer!\n";
        echo "Session established successfully.\n";
        
        // Try a simple query to verify it's working
        echo "\nTesting a simple API call...\n";
        try {
            $result = $sap->getService('CompanyService')->query()->limit(1)->find();
            if ($result->getStatusCode() === 200) {
                echo "✅ API call successful!\n";
            } else {
                echo "⚠️  API call returned status: " . $result->getStatusCode() . "\n";
            }
        } catch (Exception $e) {
            echo "⚠️  API call failed: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "❌ FAILED: Could not connect to SAP Business One Service Layer\n";
    }
} catch (Exception $e) {
    echo "❌ CONNECTION ERROR: " . $e->getMessage() . "\n";
    
    // Check if it's an SSL-related error
    if (strpos($e->getMessage(), 'SSL') !== false || 
        strpos($e->getMessage(), 'certificate') !== false ||
        strpos($e->getMessage(), 'peer') !== false) {
        
        echo "\n🔧 This appears to be an SSL-related error.\n";
        echo "For self-signed certificates, try these settings in your .env file:\n\n";
        echo "SAP_SSL_VERIFY_PEER=false\n";
        echo "SAP_SSL_VERIFY_PEER_NAME=false\n";
        echo "SAP_SSL_ALLOW_SELF_SIGNED=true\n\n";
        echo "⚠️  WARNING: Only use these settings for development/testing!\n";
        echo "📖 See SSL-TROUBLESHOOTING.md for more details.\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Test completed.\n";
