# SAP Business One Service Layer Configuration
# Copy this file to .env and update with your actual values

# Connection Settings
SAP_HTTPS=true
SAP_HOST=your-sap-server-ip-or-hostname
SAP_PORT=50000

# Authentication
SAP_USERNAME=your-sap-username
SAP_PASSWORD=your-sap-password
SAP_COMPANY=your-company-database

# SSL Configuration (optional)
# For production with valid certificates:
SAP_SSL_VERIFY_PEER=true
SAP_SSL_VERIFY_PEER_NAME=true
SAP_SSL_CAFILE=path/to/certificate.crt

# For development with self-signed certificates (NOT for production):
# SAP_SSL_VERIFY_PEER=false
# SAP_SSL_VERIFY_PEER_NAME=false
# SAP_SSL_ALLOW_SELF_SIGNED=true

# API Version
SAP_VERSION=2
