<?php

/**
 * Simple connection test - equivalent to your cURL example
 * This demonstrates the same functionality as your cURL code
 * but using the SAPb1 library with SSL bypass for self-signed certificates
 */

// Since this library doesn't have proper autoloading, include files manually
require_once 'SAPb1/Config.php';
require_once 'SAPb1/Request.php';
require_once 'SAPb1/Response.php';
require_once 'SAPb1/SAPException.php';
require_once 'SAPb1/SAPClient.php';
require_once 'SAPb1/Service.php';
require_once 'SAPb1/Query.php';

// Include filter classes
require_once 'SAPb1/Filters/Filter.php';
require_once 'SAPb1/Filters/Equal.php';
require_once 'SAPb1/Filters/Contains.php';
require_once 'SAPb1/Filters/StartsWith.php';
require_once 'SAPb1/Filters/EndsWith.php';
require_once 'SAPb1/Filters/MoreThan.php';
require_once 'SAPb1/Filters/LessThan.php';
require_once 'SAPb1/Filters/Between.php';
require_once 'SAPb1/Filters/InArray.php';
require_once 'SAPb1/Filters/NotEqual.php';
require_once 'SAPb1/Filters/NotInArray.php';
require_once 'SAPb1/Filters/Raw.php';
require_once 'SAPb1/Filters/MoreThanEqual.php';
require_once 'SAPb1/Filters/LessThanEqual.php';

echo "=== SAP B1 Connection Test (Equivalent to your cURL) ===\n\n";

// This is equivalent to your cURL settings
$config = [
    'https' => true,
    'host' => '*************',
    'port' => 50000,
    'username' => 'selva',
    'password' => '1191',
    'company' => 'CAPA',
    'version' => 2,
    'sslOptions' => [
        'verify_peer' => false,        // This bypasses SSL verification
        'verify_peer_name' => false,   // This bypasses hostname verification
        'allow_self_signed' => true,   // This allows self-signed certificates
    ]
];

echo "Configuration:\n";
echo "- URL: https://{$config['host']}:{$config['port']}/b1s/v{$config['version']}/Login\n";
echo "- Company: {$config['company']}\n";
echo "- Username: {$config['username']}\n";
echo "- SSL Bypass: Enabled (for self-signed certificates)\n\n";

try {
    // Create SAP client (this performs the login automatically)
    $sap = SAPb1\SAPClient::new($config);
    
    echo "✅ SUCCESS: Login successful!\n";
    echo "Session established with SAP Business One Service Layer.\n\n";
    
    // Show session information (equivalent to the cookies in your cURL)
    $session = $sap->getSession();
    echo "Session Details:\n";
    foreach ($session as $key => $value) {
        echo "- $key: $value\n";
    }
    
    // Test a simple API call to verify the connection is working
    echo "\n--- Testing API Call ---\n";
    $result = $sap->getService('BusinessPartners')->query()->limit(1)->find();
    
    if ($result->getStatusCode() === 200) {
        echo "✅ API call successful! Status: " . $result->getStatusCode() . "\n";
        $data = json_decode($result->getBody(), true);
        if (isset($data['value']) && count($data['value']) > 0) {
            $bp = $data['value'][0];
            echo "Sample Business Partner: {$bp['CardCode']} - {$bp['CardName']}\n";
        }
    } else {
        echo "⚠️  API call returned status: " . $result->getStatusCode() . "\n";
    }
    
} catch (SAPb1\SAPException $e) {
    echo "❌ LOGIN FAILED: " . $e->getMessage() . "\n";
    echo "This could be due to:\n";
    echo "- Incorrect credentials\n";
    echo "- SAP Service Layer not running\n";
    echo "- Network connectivity issues\n";
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Test completed.\n";
