# SAP Business One Web Client - Cursor Rules

## Project Overview
This is a **SAP Business One Web Client** application that provides a modern web interface for interacting with SAP Business One Service Layer API. The application consists of a PHP backend API and a JavaScript frontend with a responsive design.

## Architecture

### Backend Components
- **Core Library**: `SAPb1/` - PHP library for SAP B1 Service Layer integration
  - `SAPClient.php` - Main client class for SAP B1 connections
  - `Service.php` - CRUD operations for SAP B1 services
  - `Query.php` - Query builder for filtering and data retrieval
  - `Filters/` - Various filter classes (Equal, Contains, Between, etc.)
  - `Request.php` & `Response.php` - HTTP request/response handling
  - `Config.php` - Configuration management
  - `SAPException.php` - Custom exception handling

- **API Layer**: `api/` - RESTful API endpoints
  - `index.php` - Main API router with CORS handling
  - `config.php` - API configuration and environment setup
  - `classes/Environment.php` - Environment variable management
  - Controllers for different modules (auth, business partners, items, documents)

- **Configuration System**:
  - Environment-based configuration using `.env` files
  - Support for multiple SAP B1 databases (dev/production)
  - SSL/TLS configuration options
  - Session management settings

### Frontend Components
- **Main Interface**: `index.html` - Single-page application
- **Core JavaScript**: `assets/js/core/`
  - `app.js` - Main application controller and state management
  - `auth.js` - Authentication handling and session management
  - `api.js` - API communication layer
  - Navigation and routing system

- **Styling**: `assets/css/`
  - `styles.css` - Base styles and utilities
  - `login.css` - Login screen specific styles
  - `main.css` - Main application interface styles
  - Responsive design with mobile support

- **Module System**: Modular architecture for SAP B1 modules
  - Sales & A/R
  - Purchasing & A/P
  - Inventory Management
  - Business Partners
  - Banking & Financials
  - Reports & Administration

## Key Features

### Authentication & Security
- SAP B1 Service Layer authentication
- Session management with localStorage
- CORS handling for API requests
- SSL/TLS support with configurable verification
- Environment-based configuration for security

### Data Management
- Full CRUD operations for SAP B1 entities
- Query builder with advanced filtering
- Support for Business Partners, Items, Sales Orders, Purchase Orders
- Real-time data synchronization with SAP B1

### User Interface
- Modern responsive web design
- Dashboard with quick actions
- Module-based navigation
- Loading states and error handling
- Database switching (dev/production)

## Development Guidelines

### Code Style
- **PHP**: Follow PSR-4 autoloading standards
- **JavaScript**: ES6+ features, modular architecture
- **CSS**: BEM methodology for class naming
- Use meaningful variable names and comprehensive comments

### File Organization
- **Backend**: Separate controllers, models, and utilities
- **Frontend**: Modular JavaScript with clear separation of concerns
- **Configuration**: Environment-based with fallback defaults
- **Assets**: Organized by type (css, js, images)

### Database Patterns
- Use the Query builder for complex filtering
- Implement proper error handling with SAPException
- Cache frequently accessed data when appropriate
- Handle pagination for large datasets

### API Patterns
- RESTful endpoints with proper HTTP methods
- Consistent JSON response format
- Error handling with appropriate HTTP status codes
- Input validation and sanitization

## Environment Configuration

### Required Variables
```env
SAP_HOST=your-sap-server-ip
SAP_USERNAME=sap-username
SAP_PASSWORD=sap-password
SAP_COMPANY=company-database
```

### Optional Variables
```env
SAP_HTTPS=true
SAP_PORT=50000
SAP_VERSION=2
SAP_SSL_VERIFY_PEER=false
SAP_SSL_VERIFY_PEER_NAME=false
DEBUG_MODE=true
```

## Module Structure

### SAP B1 Modules Supported
- **Sales**: Sales Orders, Quotations, Deliveries, Invoices
- **Purchasing**: Purchase Orders, Goods Receipts, AP Invoices
- **Inventory**: Items, Warehouses, Stock Transfers
- **Partners**: Business Partners (Customers/Vendors)
- **Banking**: Payments, Bank Transactions
- **Financials**: Journal Entries, Chart of Accounts
- **Reports**: Various SAP B1 reports
- **Administration**: User management, system settings

### Frontend Module Pattern
Each module should follow this structure:
- Module-specific JavaScript file
- API endpoint handlers
- UI components for CRUD operations
- Data validation and error handling

## Dependencies
- **PHP**: >=7.4 with SOAP extension
- **JavaScript**: Modern browser with ES6+ support
- **SAP B1**: Service Layer API access
- **Optional**: Composer for PHP dependency management

## Security Considerations
- Never commit `.env` files to version control
- Use proper SSL certificates in production
- Implement proper input validation
- Handle sensitive data securely
- Regular security updates for dependencies

## Testing
- Test SAP B1 connectivity before deployment
- Validate API endpoints with proper data
- Test responsive design across devices
- Verify SSL/TLS configuration in production

## Deployment
- Ensure SAP B1 Service Layer is accessible
- Configure environment variables properly
- Set appropriate file permissions
- Enable error logging for production
- Configure web server (Apache/Nginx) appropriately

## Language Support
- **UI Language**: Spanish (Español)
- All user-facing text should be in Spanish
- Error messages and notifications in Spanish
- Form labels and buttons in Spanish
- Navigation and menu items in Spanish

## Common Patterns

### API Call Pattern
```javascript
try {
    showLoading(true);
    const response = await API.get('/endpoint');
    if (response.success) {
        // Handle success
    } else {
        showMessage(response.message, 'error');
    }
} catch (error) {
    showMessage('Error de conexión', 'error');
} finally {
    showLoading(false);
}
```

### SAP B1 Query Pattern
```php
$service = $sap->getService('BusinessPartners');
$result = $service->query()
    ->select('CardCode,CardName')
    ->where('CardType', 'C')
    ->orderBy('CardName')
    ->limit(50)
    ->find();
```

### Error Handling Pattern
```php
try {
    $result = $service->create($data);
    return ['success' => true, 'data' => $result->getData()];
} catch (SAPException $e) {
    return ['success' => false, 'message' => $e->getMessage()];
}
```

## Notes for Cursor AI
- This is a production SAP B1 integration application
- Maintain compatibility with SAP B1 Service Layer API
- Follow existing patterns for consistency
- All UI text should be in Spanish
- Consider SAP B1 best practices for data handling
- Environment configuration is critical for proper functioning 