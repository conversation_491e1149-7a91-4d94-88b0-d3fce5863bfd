<?php
/**
 * SAP Business One Service Layer API Test Script
 * Tests endpoints and saves JSON responses to files
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// SAP B1 Service Layer configuration
$config = [
    'base_url' => 'https://*************:50000/b1s/v2/',
    'username' => 'selva',    // SAP B1 username
    'password' => '1191',     // SAP B1 password
    'company_db' => 'CAPA',   // Database name
    'ssl_verify' => false     // Set to true in production with valid SSL
];

// Create output directory for JSON files
$output_dir = __DIR__ . '/json_responses';
if (!is_dir($output_dir)) {
    mkdir($output_dir, 0755, true);
}

// Session cookie jar to maintain session
$cookie_jar = tempnam(sys_get_temp_dir(), 'sap_cookies');

/**
 * Make HTTP request to SAP B1 Service Layer
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    global $cookie_jar, $config;
    
    $ch = curl_init();
    
    // Basic cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_COOKIEJAR => $cookie_jar,
        CURLOPT_COOKIEFILE => $cookie_jar,
        CURLOPT_HTTPHEADER => array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers),
        CURLOPT_USERAGENT => 'SAP B1 Web Client Test/1.0'
    ]);
    
    // SSL options
    if (!$config['ssl_verify']) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    
    // Set method and data
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
        case 'PATCH':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
    }
    
    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => $http_code
        ];
    }
    
    $decoded_response = json_decode($response, true);
    
    return [
        'success' => $http_code >= 200 && $http_code < 300,
        'http_code' => $http_code,
        'data' => $decoded_response ?: $response,
        'raw_response' => $response
    ];
}

/**
 * Save JSON response to file
 */
function saveJsonResponse($filename, $data, $description = '') {
    global $output_dir;
    
    $filepath = $output_dir . '/' . $filename;
    
    // Create metadata
    $output = [
        'metadata' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'description' => $description,
            'endpoint' => $data['endpoint'] ?? 'unknown',
            'http_code' => $data['http_code'] ?? 0,
            'success' => $data['success'] ?? false
        ],
        'response' => $data['data'] ?? $data
    ];
    
    $json = json_encode($output, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($filepath, $json);
    
    echo "💾 Saved response to: {$filename}\n";
    return $filepath;
}

/**
 * Login to SAP B1 Service Layer
 */
function login() {
    global $config;
    
    echo "🔐 Attempting to login to SAP B1 Service Layer...\n";
    echo "Server: {$config['base_url']}\n";
    echo "Database: {$config['company_db']}\n";
    echo "Username: {$config['username']}\n";
    
    $login_data = [
        'CompanyDB' => $config['company_db'],
        'UserName' => $config['username'],
        'Password' => $config['password']
    ];
    
    $url = $config['base_url'] . 'Login';
    $result = makeRequest($url, 'POST', $login_data);
    $result['endpoint'] = 'Login';
    
    // Save login response
    saveJsonResponse('login_response.json', $result, 'SAP B1 Service Layer Login Response');
    
    if ($result['success']) {
        echo "✅ Login successful!\n";
        if (isset($result['data']['SessionId'])) {
            echo "Session ID: {$result['data']['SessionId']}\n";
        }
        return true;
    } else {
        echo "❌ Login failed!\n";
        echo "HTTP Code: {$result['http_code']}\n";
        if (isset($result['error'])) {
            echo "Error: {$result['error']}\n";
        }
        if (isset($result['data']['error'])) {
            echo "SAP Error: {$result['data']['error']['message']['value']}\n";
        }
        return false;
    }
}

/**
 * Test specific endpoint and save response
 */
function testEndpoint($endpoint, $filename, $description) {
    global $config;
    
    echo "\n📋 Testing {$endpoint}...\n";
    
    $url = $config['base_url'] . $endpoint;
    $result = makeRequest($url, 'GET');
    $result['endpoint'] = $endpoint;
    
    // Save response to JSON file
    saveJsonResponse($filename, $result, $description);
    
    if ($result['success']) {
        echo "✅ Success (HTTP {$result['http_code']})\n";
        
        if (isset($result['data']['value']) && is_array($result['data']['value'])) {
            $count = count($result['data']['value']);
            echo "Records found: {$count}\n";
        }
        
        // Show sample data structure
        if (isset($result['data']['value'][0])) {
            $sample = $result['data']['value'][0];
            echo "Sample fields: " . implode(', ', array_keys($sample)) . "\n";
        }
    } else {
        echo "❌ Failed (HTTP {$result['http_code']})\n";
        if (isset($result['data']['error'])) {
            echo "Error: {$result['data']['error']['message']['value']}\n";
        }
    }
    
    return $result['success'];
}

/**
 * Logout from SAP B1 Service Layer
 */
function logout() {
    global $config, $cookie_jar;
    
    echo "\n🚪 Logging out...\n";
    
    $url = $config['base_url'] . 'Logout';
    $result = makeRequest($url, 'POST');
    $result['endpoint'] = 'Logout';
    
    // Save logout response
    saveJsonResponse('logout_response.json', $result, 'SAP B1 Service Layer Logout Response');
    
    if ($result['success']) {
        echo "✅ Logout successful!\n";
    } else {
        echo "⚠️ Logout request completed (HTTP {$result['http_code']})\n";
    }
    
    // Clean up cookie jar
    if (file_exists($cookie_jar)) {
        unlink($cookie_jar);
    }
}

// Start the test
echo "🚀 SAP Business One Service Layer API Test\n";
echo "==========================================\n";

try {
    // Step 1: Login
    if (login()) {
        echo "\n" . str_repeat("-", 50) . "\n";
        
        // Define endpoints to test
        $endpoints = [
            [
                'endpoint' => 'UserObjectsMD',
                'filename' => 'user_objects_md.json',
                'description' => 'User Defined Objects Metadata'
            ],
            [
                'endpoint' => 'BusinessPartners?$top=10',
                'filename' => 'business_partners.json',
                'description' => 'Business Partners (Top 10)'
            ],
            [
                'endpoint' => 'Items?$top=10',
                'filename' => 'items.json',
                'description' => 'Items Master Data (Top 10)'
            ],
            [
                'endpoint' => 'Orders?$top=10',
                'filename' => 'sales_orders.json',
                'description' => 'Sales Orders (Top 10)'
            ],
            [
                'endpoint' => 'PurchaseOrders?$top=10',
                'filename' => 'purchase_orders.json',
                'description' => 'Purchase Orders (Top 10)'
            ],
            [
                'endpoint' => 'Warehouses',
                'filename' => 'warehouses.json',
                'description' => 'Warehouses Master Data'
            ],
            [
                'endpoint' => 'ChartOfAccounts?$top=20',
                'filename' => 'chart_of_accounts.json',
                'description' => 'Chart of Accounts (Top 20)'
            ],
            [
                'endpoint' => 'PaymentTermsTypes',
                'filename' => 'payment_terms.json',
                'description' => 'Payment Terms Types'
            ],
            [
                'endpoint' => 'SalesPersons',
                'filename' => 'sales_persons.json',
                'description' => 'Sales Persons Master Data'
            ],
            [
                'endpoint' => 'VatGroups',
                'filename' => 'vat_groups.json',
                'description' => 'VAT Groups Configuration'
            ]
        ];
        
        // Test each endpoint
        foreach ($endpoints as $test) {
            testEndpoint($test['endpoint'], $test['filename'], $test['description']);
            echo str_repeat("-", 50) . "\n";
        }
        
        // Logout
        logout();
    }
    
    echo "\n✅ Test completed!\n";
    echo "📁 JSON responses saved in: {$output_dir}\n";
    
    // List all saved files
    $files = glob($output_dir . '/*.json');
    echo "\n📋 Generated files:\n";
    foreach ($files as $file) {
        $size = round(filesize($file) / 1024, 2);
        echo "  • " . basename($file) . " ({$size} KB)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed with exception!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n🎯 Summary saved to json_responses/ directory\n";
?>
