<?php
/**
 * SAP Business One Web Client API Configuration
 */

// Load environment configuration
require_once __DIR__ . '/classes/Environment.php';
Environment::load();

// Environment settings
define('DEBUG_MODE', Environment::isDebug());
define('SESSION_TIMEOUT', Environment::getSessionTimeout());

// SAP B1 Service Layer Configuration
define('SAP_BASE_URL', Environment::getSAPBaseUrl());
define('SAP_SSL_VERIFY', Environment::isSSLVerifyEnabled());

// Database configurations
$SAP_DATABASES = Environment::getDatabaseConfig();

// Session storage (in production, use Redis or database)
$SESSION_STORAGE = [
    'type' => Environment::get('SESSION_STORAGE_TYPE', 'file'), // file, redis, database
    'path' => sys_get_temp_dir() . '/sap_sessions/',
    'prefix' => 'sap_session_'
];

// API Response configurations
$API_CONFIG = Environment::getApiConfig();

// Logging configuration
$LOG_CONFIG = array_merge(Environment::getLogConfig(), [
    'file' => __DIR__ . '/../logs/api.log'
]);

// Create logs directory if it doesn't exist
if ($LOG_CONFIG['enabled'] && !is_dir(dirname($LOG_CONFIG['file']))) {
    mkdir(dirname($LOG_CONFIG['file']), 0755, true);
}

// Error handler for logging
if ($LOG_CONFIG['enabled']) {
    set_error_handler(function($severity, $message, $file, $line) use ($LOG_CONFIG) {
        if (error_reporting() & $severity) {
            $log_message = date('Y-m-d H:i:s') . " [ERROR] {$message} in {$file}:{$line}" . PHP_EOL;
            error_log($log_message, 3, $LOG_CONFIG['file']);
        }
    });
}

/**
 * Get configuration value
 */
function getConfig($key, $default = null) {
    global $API_CONFIG;
    return isset($API_CONFIG[$key]) ? $API_CONFIG[$key] : $default;
}

/**
 * Log message
 */
function logMessage($message, $level = 'info') {
    global $LOG_CONFIG;
    
    if (!$LOG_CONFIG['enabled']) {
        return;
    }
    
    $log_message = date('Y-m-d H:i:s') . " [{$level}] {$message}" . PHP_EOL;
    error_log($log_message, 3, $LOG_CONFIG['file']);
    
    // Rotate log file if it's too large
    if (file_exists($LOG_CONFIG['file']) && filesize($LOG_CONFIG['file']) > $LOG_CONFIG['max_size']) {
        rename($LOG_CONFIG['file'], $LOG_CONFIG['file'] . '.old');
    }
}

/**
 * Get database configuration
 */
function getDatabaseConfig($database) {
    global $SAP_DATABASES;
    return isset($SAP_DATABASES[$database]) ? $SAP_DATABASES[$database] : null;
}

/**
 * Validate required environment
 */
function validateEnvironment() {
    $required_extensions = ['curl', 'json', 'openssl'];
    $missing = [];
    
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing[] = $ext;
        }
    }
    
    if (!empty($missing)) {
        throw new Exception('Missing required PHP extensions: ' . implode(', ', $missing));
    }
    
    return true;
}

// Validate environment on load
try {
    validateEnvironment();
} catch (Exception $e) {
    logMessage($e->getMessage(), 'error');
    if (DEBUG_MODE) {
        throw $e;
    }
}
?>
