<?php
/**
 * Frontend Configuration API Endpoint
 * Returns configuration for the frontend application
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Load environment configuration
require_once __DIR__ . '/classes/Environment.php';
Environment::load();

try {
    $config = [
        'server' => [
            'default_url' => Environment::getDefaultServerUrl(),
            'ssl_verify' => Environment::isSSLVerifyEnabled()
        ],
        'database' => [
            'default' => Environment::getDefaultDatabase(),
            'options' => Environment::getDatabaseConfig()
        ],
        'api' => [
            'base_url' => '/api',
            'timeout' => Environment::get('SAP_TIMEOUT', 30),
            'retry_attempts' => Environment::get('SAP_RETRY_ATTEMPTS', 3)
        ],
        'session' => [
            'timeout' => Environment::getSessionTimeout()
        ],
        'app' => [
            'name' => Environment::get('APP_NAME', 'SAP Business One Web Client'),
            'env' => Environment::getAppEnv(),
            'debug' => Environment::isDebug()
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $config
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Configuration error',
        'message' => Environment::isDebug() ? $e->getMessage() : 'Unable to load configuration'
    ]);
}
?>
