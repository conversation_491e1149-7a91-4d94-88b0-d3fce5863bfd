<?php
/**
 * Environment Configuration Loader
 * Loads configuration from .env file
 */
class Environment {
    
    private static $loaded = false;
    private static $config = [];
    
    /**
     * Load environment variables from .env file
     */
    public static function load($envFile = null) {
        if (self::$loaded) {
            return;
        }
        
        if ($envFile === null) {
            $envFile = dirname(__DIR__) . '/.env';
        }
        
        if (!file_exists($envFile)) {
            throw new Exception("Environment file not found: {$envFile}");
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (($value[0] === '"' && $value[-1] === '"') || 
                    ($value[0] === "'" && $value[-1] === "'")) {
                    $value = substr($value, 1, -1);
                }
                
                // Convert boolean strings
                $value = self::convertType($value);
                
                // Set environment variable
                $_ENV[$key] = $value;
                self::$config[$key] = $value;
                
                // Also set as putenv for compatibility
                putenv("{$key}={$value}");
            }
        }
        
        self::$loaded = true;
    }
    
    /**
     * Get environment variable with default value
     */
    public static function get($key, $default = null) {
        if (!self::$loaded) {
            self::load();
        }
        
        // Check $_ENV first, then self::$config, then return default
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }
        
        if (isset(self::$config[$key])) {
            return self::$config[$key];
        }
        
        return $default;
    }
    
    /**
     * Get all environment variables
     */
    public static function all() {
        if (!self::$loaded) {
            self::load();
        }
        
        return self::$config;
    }
    
    /**
     * Check if environment variable exists
     */
    public static function has($key) {
        if (!self::$loaded) {
            self::load();
        }
        
        return isset($_ENV[$key]) || isset(self::$config[$key]);
    }
    
    /**
     * Convert string values to appropriate types
     */
    private static function convertType($value) {
        // Handle boolean values
        $lower = strtolower($value);
        if ($lower === 'true') {
            return true;
        }
        if ($lower === 'false') {
            return false;
        }
        
        // Handle null
        if ($lower === 'null') {
            return null;
        }
        
        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float)$value : (int)$value;
        }
        
        return $value;
    }
    
    /**
     * Set environment variable
     */
    public static function set($key, $value) {
        $_ENV[$key] = $value;
        self::$config[$key] = $value;
        putenv("{$key}={$value}");
    }
    
    /**
     * Get SAP B1 Service Layer URL
     */
    public static function getSAPBaseUrl() {
        return self::get('SAP_BASE_URL', 'https://*************:50000/b1s/v2/');
    }
    
    /**
     * Get default server URL for frontend
     */
    public static function getDefaultServerUrl() {
        return self::get('DEFAULT_SERVER_URL', 'https://*************:50000/b1s/v2');
    }
    
    /**
     * Get database configuration
     */
    public static function getDatabaseConfig() {
        return [
            'dev' => [
                'name' => self::get('SAP_DEV_DATABASE', 'CAPA'),
                'description' => 'Development Database',
                'type' => 'development'
            ],
            'prod' => [
                'name' => self::get('SAP_PROD_DATABASE', 'SBO_ECOM'),
                'description' => 'Production Database',
                'type' => 'production'
            ]
        ];
    }
    
    /**
     * Get default database
     */
    public static function getDefaultDatabase() {
        return self::get('DEFAULT_DATABASE', 'CAPA');
    }
    
    /**
     * Check if we're in debug mode
     */
    public static function isDebug() {
        return self::get('APP_DEBUG', true);
    }
    
    /**
     * Get application environment
     */
    public static function getAppEnv() {
        return self::get('APP_ENV', 'development');
    }
    
    /**
     * Check if SSL verification is enabled
     */
    public static function isSSLVerifyEnabled() {
        return self::get('SAP_SSL_VERIFY', false);
    }
    
    /**
     * Get session timeout
     */
    public static function getSessionTimeout() {
        return self::get('SESSION_TIMEOUT', 1800);
    }
    
    /**
     * Get API configuration
     */
    public static function getApiConfig() {
        return [
            'default_page_size' => self::get('API_DEFAULT_PAGE_SIZE', 20),
            'max_page_size' => self::get('API_MAX_PAGE_SIZE', 100),
            'timeout' => self::get('SAP_TIMEOUT', 30),
            'retry_attempts' => self::get('SAP_RETRY_ATTEMPTS', 3)
        ];
    }
    
    /**
     * Get logging configuration
     */
    public static function getLogConfig() {
        return [
            'enabled' => self::get('LOG_ENABLED', true),
            'level' => self::get('LOG_LEVEL', self::isDebug() ? 'debug' : 'error'),
            'max_size' => self::get('LOG_MAX_SIZE', 10485760)
        ];
    }
}
